import asyncio
import logging
import signal
import time
from abc import ABC, abstractmethod
from collections.abc import Callable, Coroutine
from datetime import UTC, datetime, timedelta
from typing import Any, Generic, Literal, Required, TypedDict, TypeVar

from utils_general import log_bsdebug

from block_stream.agent import Agent
from block_stream.config import ACTIVE_FUNCTION_WATCHDOG_S, BLOCKSTREAM_ENDPOINT
from block_stream.utils.ecs_cluster_manager import EcsClusterManager
from block_stream.utils.locks import LazyInitLock


class Subscriber(TypedDict, total=False):
    last_heartbeat: datetime


class ConsumerSubMessage(TypedDict):
    command: str
    calculator: str
    function_id: Required[str]
    subscriber_id: Required[str]


M = TypeVar("M", bound=ConsumerSubMessage)
S = TypeVar("S", bound=Subscriber)


class ConsumerBaseFunctionClass(ABC, Generic[M, S]):
    def __init__(self, message: M, function_id: str | None = None) -> None:
        self.message = message
        self._subscribers: dict[str, S] = {}

    @abstractmethod
    def subscribe(self, message: M) -> None:
        pass

    @abstractmethod
    def unsubscribe(self, message: M) -> int:
        pass

    @abstractmethod
    async def execute(self) -> Any:
        pass

    @abstractmethod
    def get_total_executions(self) -> int:
        return 1

    def is_expired(self, refresh_s: int) -> bool:
        if not self._subscribers:
            return True
        last_heartbeat = max([s["last_heartbeat"] for s in self._subscribers.values()])
        if datetime.now(tz=UTC) - last_heartbeat > timedelta(seconds=refresh_s):
            return True
        else:
            return False


ProcessorFn = Callable[..., Coroutine[Any, Any, Any]]

T = TypeVar("T", bound=ConsumerBaseFunctionClass[Any, Any])


class ConsumerSubscriptionManager(ABC, Generic[T, M]):
    def __init__(
        self,
        calculator: str,
        function_class: type[T],
        ecs_cluster: Literal["Staging", "Production"] | str,
        ecs_service: str,
        watchdog_secs: int = 7200,  # 2hrs
        task_info_cache_sec: int = 0,
    ) -> None:
        """
        Sets up a subscription manager which handles subscriptions across multi container deployments.

        :param calculator: Calculator used to listen on the internal stream
        :param function_class: Defines the methods of the function Class used within the service to store the subscribers and business logic
        :param ecs_cluster: Cluster we are running the stream context on (Usually Staging or Production, but left open incase more clusters)
        :param ecs_service: Name of service subscription manager is running on, used to get a concept of amount of other tasks running (e.g. volSmileFlexStream-service)
        :param watchdog_secs: Seconds used before clearing out stale function nodes, when a entity subscribes an epoch is inputed,
        the seconds provided here is how long after the max epoch.
        :param task_info_cache_sec: Optional parameter to specify the number of seconds to keep task information cached
        before requesting it again from ECS.

        :return:
        """

        self._calculator = calculator
        self._function_class = function_class
        self._ecs_cluster = ecs_cluster
        self._ecs_service = ecs_service
        self._active_functions: dict[str, T] = {}
        self._watchdog_secs = watchdog_secs
        self._active_fn_lock = LazyInitLock()
        self._sending_retransmit = False
        self._agent = Agent(
            f"{calculator}Calc", endpoint=BLOCKSTREAM_ENDPOINT, consumer_mode="fanout"
        )

        self._internal_data_stream = self._agent.channel(
            "blockstreamInternal",
            consumer_mode="unregistered",
        )

        self._cluster_manager = EcsClusterManager(
            task_info_cache_sec=task_info_cache_sec
        )

    async def async_run(self) -> None:
        # setup teardown fn
        loop = asyncio.get_event_loop()
        loop.add_signal_handler(signal.SIGTERM, self._sigterm_handler)

        # Toggle this flag here as we should never be sending a retransmit before calling the async_run()
        # It is possible that that flag was toggled on and left that way by a previous process which raised an exception
        # and was restarted. The ConsumerSubscriptonManager is not reinitialised when this happens which leads to the
        # flag being set to True when the async_run is re-entered
        self._sending_retransmit = False

        await asyncio.gather(
            self.internal_listener(),
            self.expired_function_watchdog(),
            self._internal_data_stream.list_streams_worker(),
            self._send_retransmit_subscriptions_with_delay(),
        )

    async def _add_function(self, id: str, sub: T) -> None:
        async with self._active_fn_lock:
            self._active_functions[id] = sub

    def _delete_function(self, id: str) -> None:
        if not id or id not in self._active_functions:
            logging.error(f"Unable to delete, function not found: {id}")
            return
        del self._active_functions[id]

    @abstractmethod
    def on_delete_fn(self) -> Any:
        pass

    async def setup_new_function(self, msg: M) -> None:
        """
        Sets up a new function and subscribes the first user to it. It'll
        also set up the required data sources

        :param msg:
        :return:
        """
        log_bsdebug(
            "New function %s created by %s",
            msg["function_id"],
            msg["subscriber_id"],
        )

        sub = self._function_class(function_id=msg["function_id"], message=msg)
        # Subscribes the first user to the function
        sub.subscribe(msg)

        # Tracks all actively evaluated functions
        await self._add_function(msg["function_id"], sub)

    async def should_handle_fn_fan_out(self, function_id: str) -> bool:
        ecs_stats = await self._cluster_manager.get_task_info()

        if ecs_stats["running_task_count"] <= 1:
            return True

        # Convert MD5 hash to int and mod by number of instances
        function_mod = int(function_id, 16) % ecs_stats["running_task_count"]
        log_bsdebug(
            "function_mod=%d, current_task_index=%d",
            function_mod,
            ecs_stats["current_task_index"],
        )

        if function_mod != ecs_stats["current_task_index"]:
            if function_id in self._active_functions:
                logging.info(
                    f"Deleting function due to handling by another instance: {function_id}"
                )
                await self.delete_function(function_id)
            else:
                logging.info(
                    f"Ignoring function {function_id}, handled by #{function_mod}"
                )

            return False

        return True

    async def subscribe(self, msg: M) -> Any:
        """
        Subscribes a consumer to a function and sets up the data sources.
        Will ignore the message if the function is handled by another instance.

        :param msg: Base message with extra properties specific to each command. function_id,
        subscriber_id are musts and used to see if already subscribed.

        :return: N/A
        """

        # Ignore if handled by another instance
        if not await self.should_handle_fn_fan_out(msg["function_id"]):
            # If another instance has triggered a retransmit and the current instance previously calculated it,
            # then unload work load.
            active_function = self.get_function(msg["function_id"])
            if active_function:
                await self.unsubscribe(msg)
            return

        # Check if function is already active, otherwise set up
        async with self._active_fn_lock:
            active_function = self.get_function(msg["function_id"])
            if active_function:
                active_function.subscribe(msg)
                log_bsdebug(
                    "Running function %s subscribed by %s",
                    msg["function_id"],
                    msg["subscriber_id"],
                )
                result = await active_function.execute()

                return result

            else:
                try:
                    await self.setup_new_function(msg=msg)
                    log_bsdebug(
                        "New function %s subscribed by %s",
                        msg["function_id"],
                        msg["subscriber_id"],
                    )
                    return await self._active_functions[msg["function_id"]].execute()
                except Exception as e:
                    # TODO: temporary hack due to very large number of functions with no historic data
                    logging.exception(f"Unable to set up function: {e}")

    async def delete_function(self, function_id: str) -> None:
        if function_id not in self._active_functions:
            logging.error(f"Unable to delete, function not found: {function_id}")
            return
        async with self._active_fn_lock:
            await self.on_delete_fn()
            # Remove function
            self._delete_function(function_id)
            logging.info(f"Deleted function: {function_id}")

    async def unsubscribe(self, msg: M) -> None:
        function_id = msg["function_id"]

        function = self.get_function(function_id)
        if not function:
            logging.error("Unable to unsubscribe, not subscribed")
            return
        async with self._active_fn_lock:
            remain_subscribers = function.unsubscribe(msg)
            if remain_subscribers == 0:
                await self.delete_function(function_id=function_id)

            logging.info(
                f"Unsubscribed: {msg['subscriber_id']} from {msg['function_id']}"
            )

    def get_active_function_count(self) -> int:
        return len(self._active_functions)

    def get_total_executions(self) -> int:
        total = 0
        for fn in self._active_functions.values():
            total += fn.get_total_executions()

        return total

    def get_function(self, function_id: str) -> T | None:
        """
        Returns the function (with an active subscription)
        for a given function id

        :param function_id: Function id
        :return: ActiveFunction
        """

        if not function_id or function_id not in self._active_functions:
            return None

        return self._active_functions[function_id]

    def get_all_functions(self) -> dict[str, T]:
        return self._active_functions.copy()

    async def expired_function_watchdog(self) -> None:
        """
        Removes expired expiries from the active functions (bybit.BTC.SVI)

        :return: N/A
        """

        next_iter = (
            time.time() // ACTIVE_FUNCTION_WATCHDOG_S * ACTIVE_FUNCTION_WATCHDOG_S
        )

        while True:
            try:
                sleep_time = next_iter - time.time()
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

                log_bsdebug("ConsumerSubscriptionManager watchdog iteration")

                # Avoids modifying dict while iterating
                for function_id, function in list(self._active_functions.items()):
                    if function.is_expired(refresh_s=self._watchdog_secs):
                        await self.delete_function(function_id)
                        logging.info(f"Removed expired function: {function_id}")

                next_iter += ACTIVE_FUNCTION_WATCHDOG_S
            except Exception as e:
                logging.exception(f"Expired function watchdog failed: {e}")

    async def _handle_command(self, msg: M) -> None:
        if msg.get("calculator") != self._calculator:
            return

        if msg["command"] == "subscribe":
            await self.subscribe(msg)

        elif msg["command"] == "unsubscribe":
            await self.unsubscribe(msg)

        elif msg["command"] == "task_shutdown":
            # Note: This command lets us detect when other tasks are shutting down and send a retransmit message after
            # deployment completes. It covers both scale-down/up events and new deployments.
            await self._send_retransmit_subscriptions_with_delay()

    async def internal_listener(self) -> None:
        # Listen to subscribe/unsubscribe commands
        async for msg in self._internal_data_stream:
            try:
                await self._handle_command(msg)
            except Exception as e:
                logging.exception(f"Unable to handle command: {e}: {msg}")

    def _sigterm_handler(self) -> None:
        """Handle termination signal and run async cleanup function."""
        logging.info("Handling termination signal")
        # store a reference to the _cleanup_task to prevent it from being garbage collected
        self._cleanup_task = asyncio.create_task(self._async_cleanup())
        self._cleanup_task.add_done_callback(self._on_cleanup_done)

    def _on_cleanup_done(self, task: "asyncio.Task[None]") -> None:
        try:
            task.result()
            logging.info("Cleanup task finished successfully.")
        except Exception as _err:
            logging.exception(f"Error while runnign cleanup task: {_err}")

    async def _async_cleanup(self) -> None:
        # On Shutdown send retransmit message to rebalance tasks
        logging.info(f"Sending shutdown command on close: {self._calculator}")
        await self._internal_data_stream.put(
            {
                "command": "task_shutdown",
                "calculator": self._calculator,
                "task_id": self._cluster_manager.get_current_task_id(),
            }
        )
        await self._internal_data_stream.flush()

    async def _send_retransmit_subscriptions_with_delay(self) -> None:
        """
        Sending retransmit subs message with delay to handle missed subscriptions messages on a (re)deploy event.

        Problem: We query ECS metadata to determine how many tasks are running within a given service (and calculate
        an index per task based on that too). This is then used to "route" live functions to different instances.
        On a (re)deploy event, there seems to be a sporadic event, when for a period of time both the old and new
        instances are still up, and not properly filtered out by this ECS metadata query messing up the routing.
        """
        if self._sending_retransmit:
            logging.info(
                "Retransmit request skipped: another retransmit is already pending."
            )
            return

        # Prevent sending multiple retransmit messages simultaneously after deployment completion
        self._sending_retransmit = True

        ecs_stats = await self._cluster_manager.get_task_info()
        while (
            ecs_stats["desired_task_count"] != ecs_stats["running_task_count"]
            or ecs_stats["pending_task_count"] > 0
        ):
            logging.info(
                f"Waiting until deployment finish before sending retransmit subscriptions message to internal stream. "
                f"(desired, running, pending) -> ({ecs_stats['desired_task_count']}, {ecs_stats['running_task_count']}, {ecs_stats['pending_task_count']})"
            )
            await asyncio.sleep(1)
            ecs_stats = await self._cluster_manager.get_task_info()

        logging.info("Sending retransmit subscriptions message to internal stream.")

        # Request retransmission of all active subscriptions
        await self._internal_data_stream.put(
            {"command": "retransmit", "calculator": self._calculator}
        )
        await self._internal_data_stream.flush()

        self._sending_retransmit = False
