import asyncio
import atexit
import logging
import math
import time
from collections import defaultdict
from collections.abc import Callable, Collection
from concurrent.futures import BrokenExecutor, Future, ProcessPoolExecutor, process
from datetime import timedelta
from functools import partial
from multiprocessing import active_children
from typing import (
    Any,
    ParamSpec,
    Protocol,
    TypeVar,
    cast,
    runtime_checkable,
)

import pandas as pd
from utils_general import log_bsdebug, to_datetime, to_iso

from block_stream.channel import Channel
from block_stream.config import NUM_WORKERS
from block_stream.consumer.auto_scaling_cache_manager import (
    AutoScalingCacheManager,
    CacheT,
    T,
)
from block_stream.consumer.cache import (
    CalculationCache,
    WaitForSetCalculationCache,
)
from block_stream.consumer.typings import (
    CalculationOutput,
    ConsumerMode,
    PrepData,
    ProcessData,
    PutOutput,
)
from block_stream.utils.metrics import MetricWorker, MultiLagMetric, RateMetric

_P = ParamSpec("_P")
_T = TypeVar("_T")


@runtime_checkable
class ConsumerPool(Protocol):
    def shutdown(self, wait: bool = True) -> None: ...

    def submit(
        self, fn: Callable[_P, _T], /, *args: _P.args, **kwargs: _P.kwargs
    ) -> Future[_T]: ...


def _run_calc(  # globally defined so we can pickle
    name: str,
    timestamp: int,
    prep_data: PrepData,
    process_chunk_helper: ProcessData,
    cache_data: dict[str, Any],
) -> tuple[dict[str, tuple[int, int]], CalculationOutput] | None:
    try:
        logging.getLogger().handlers[0].formatter = logging.Formatter(
            f"%(asctime)s [%(levelname)s:%(process)d] - {name} - %(message)s"
        )

        # start_calc_lag: from timestamp until _run_calc is executed
        metric_output: dict[str, tuple[int, int]] = {
            "start_calc_lag": (timestamp, time.time_ns())
        }

        data: Any = prep_data(snapshot_ts=timestamp, **cache_data)

        # prep_data_lag: from start_calc until prep_data ends
        metric_output["prep_data_lag"] = (
            metric_output["start_calc_lag"][1],
            time.time_ns(),
        )

        if isinstance(data, tuple):
            data = data[0]

        calc_output = process_chunk_helper(
            chunk=data, snapshot_ts=timestamp, **cache_data
        )

        # process_data_lag: from prep_data ends until process_data ends
        metric_output["process_data_lag"] = (
            metric_output["prep_data_lag"][1],
            time.time_ns(),
        )

        if isinstance(calc_output, tuple):
            calc_output = calc_output[0]

        prep_time_ms = (
            metric_output["prep_data_lag"][1] - metric_output["prep_data_lag"][0]
        ) / 1e6
        process_time_ms = (
            metric_output["process_data_lag"][1] - metric_output["process_data_lag"][0]
        ) / 1e6
        total_time_ms = (
            metric_output["process_data_lag"][1] - metric_output["prep_data_lag"][0]
        ) / 1e6
        log_bsdebug(
            "Calc done prep_time_ms=%.3fms | process_time_ms=%.3fms | total_time_ms=%.3fms",
            prep_time_ms,
            process_time_ms,
            total_time_ms,
        )
        return metric_output, calc_output

    except Exception as e:
        logging.exception(f"Error while running calc {timestamp=} {e}")
        return None


async def _put_output(
    output_data_stream: Channel,
    output: list[Any] | pd.DataFrame,
) -> None:
    is_dataframe = type(output) is pd.DataFrame

    for row in output.itertuples(index=False) if is_dataframe else output:  # type: ignore
        if is_dataframe:
            qualified_name = cast(str, row.qualified_name)
            timestamp = cast(int, row.timestamp)
        else:
            qualified_name = cast(str, row["qualified_name"])
            timestamp = cast(int, row["timestamp"])

        split = qualified_name.split(".")
        key = ".".join(split[:2])
        value_label = split[-1]

        result = {
            "q": qualified_name,
            "t": timestamp,
            "v": cast(
                float, getattr(row, value_label) if is_dataframe else row[value_label]
            ),
        }

        log_bsdebug("put: %s onto %s", result, key)
        await output_data_stream.put(result, key=key)

    await output_data_stream.flush()


class Consumer:
    """
    This class wraps a lambda calc to enable it to run within the streaming context.
    """

    def __init__(
        self,
        consumer_name: str,
        input_data_stream: Channel,
        output_data_stream: Channel,
        mode: ConsumerMode,
        caches: (
            Collection[CalculationCache]
            | Collection[WaitForSetCalculationCache]
            | AutoScalingCacheManager[T, CacheT]
        ),
        prep_data: PrepData,
        process_chunk_helper: ProcessData,
        frequency_ns: int | None = None,
        clean_up_interval_secs: int | None = 3600,
        put_output: PutOutput = _put_output,
        metric_seconds: int = 60,
        copy_data_on_calc: bool = False,
        allowed_calc_lag_metric_stages: list[str] | None = None,
        log_empty_output_error: bool = True,
    ) -> None:
        """
        :param consumer_name: Name of the stream, used for metric collection.
        :param input_data_stream: Given input data stream, e.g. tickData.
        :param output_data_stream: Given output data steam e.g. calculatedData.
        :param mode: Mode of operation:
            - "fixed" = run the calculation at a fixed frequency.
            - "wait-for-set" = *Has to be used with a historic cache due to waiting for timestamps* [TIMESTAMP SENSITIVE] Waits for qfns
                                in the given set to arrive with EXACT timestamp. Good for processes after a "timestamp" mode output.
                                Set frequency_ns to define the max time waiting between flushing old timestamps.
            - "timestamp" = run the calculation when the timestamp of the received datapoint is over the end of
                            the current snapshot. This means that the calculation will be run at regular intervals,
                            but the exact time of calculation will be based on the arrival of the datapoints
                            rather than a fixed frequency. This approach is good for reproducibility, but it is not
                            suitable for high-frequency applications.

        :param contexts: Calculation contexts.
        :param prep_data: Callable fn to prepare data.
        :param process_chunk_helper: Callable fn that process chunks of data with your calc.
        :param frequency_ns: Frequency of calculation window in nanoseconds.
        :param clean_up_interval_secs: Interval for calling the caches clean_up() method. Set to 0 to disable.
        :param put_output: Function to put the calculation result onto the output data stream.
        :param metric_seconds: Seconds used to send metrics/collect over, defaults to 60.
        :param copy_data_on_calc: Enable/disable copying the cache data before firing a calculation. Always enabled in 'wait-for-set' mode.
        :param allowed_calc_lag_metric_stages: List of allowed metric stages to output. By default, we register total_lag
        :param log_empty_output_error: If True, we log an error if the output of the calc is empty
        """
        if mode == "wait-for-set":
            if not frequency_ns:
                frequency_ns = int(10 * 1e9)

            copy_data_on_calc = True

        if not frequency_ns:
            raise ValueError("frequency_ns must be set")

        self._name = consumer_name
        self._input_data_stream = input_data_stream
        self._output_data_stream = output_data_stream
        self._mode = mode
        self._frequency_ns = frequency_ns
        self._caches = caches
        self._prep_data = prep_data
        self._process_chunk_helper = process_chunk_helper
        self._clean_up_interval_secs = clean_up_interval_secs
        self._put_output = put_output
        self._log_empty_output_warning = log_empty_output_error
        self._pool: ConsumerPool = ProcessPoolExecutor(max_workers=NUM_WORKERS)
        self._running_tasks: dict[str, set[asyncio.Task[None]]] = defaultdict(set)
        self._copy_data_on_calc = copy_data_on_calc

        if allowed_calc_lag_metric_stages is None:
            allowed_calc_lag_metric_stages = ["total_lag"]

        self._calc_lag_metric = MultiLagMetric(
            fields=["calc_name", "stage"],
            allowed_fields_values={"stage": allowed_calc_lag_metric_stages},
            precision="ms",
        )
        self._input_data_lag_metric = MultiLagMetric(fields=["exchange", "asset_class"])
        self._processed_datapoints_metric = RateMetric()
        self._metric_worker = MetricWorker(
            metric_seconds,
            {
                "calculation_lag": self._calc_lag_metric,
                "input_data_lag": self._input_data_lag_metric,
                "processed_datapoints": self._processed_datapoints_metric,
            },
            component_name=consumer_name,
        )
        # Initialize next_run_time for all caches so they can be triggered independently instead
        # of only the first one if the consumer stored a singular next run time.
        for cache in self._caches:
            cache.next_run_time = self._get_next_snapshot_start()

        # Decoupling from class https://stackoverflow.com/questions/16333054/what-are-the-implications-of-registering-an-instance-method-with-atexit-in-pytho
        def _cleanup() -> None:
            logging.info(f"Cleaning up {len(active_children())=}")

            for proc in active_children():
                proc.terminate()

            logging.info(f"CLEANED {len(active_children())=}")

        atexit.register(_cleanup)

    async def async_run(self) -> None:
        await asyncio.gather(
            self._async_run(),
            self._remove_old_instruments_watcher(),
            self._metric_worker.run(),
            self._output_data_stream.list_streams_worker(),
        )

    def run(self) -> None:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self.async_run())

    # Tests can use this to run the Consumer
    async def _async_run(self) -> None:
        # Special callback for the CacheManager + run the Manager itself
        if isinstance(self._caches, AutoScalingCacheManager):
            await asyncio.gather(self._caches.async_run(), self._run_downsampler())
        else:
            await self._run_downsampler()

    async def _remove_old_instruments_watcher(self) -> None:
        if not self._clean_up_interval_secs:
            return

        refresh_interval = self._clean_up_interval_secs
        next_refresh = int((time.time() // refresh_interval) * refresh_interval)
        logging.info(f"Starting stale instrument watchdog {refresh_interval=}s")

        while True:
            try:
                next_refresh += refresh_interval
                wait_time = next_refresh - time.time()

                if wait_time > 0:
                    await asyncio.sleep(wait_time)

                for cache in self._caches:
                    cache.on_clean_up()

            except Exception as e:
                logging.exception(f"Unable to remove stale instruments: {e}")

    async def _run_downsampler(self) -> None:
        if self._mode == "timestamp":
            await self._run_timestamp_downsampler()
        elif self._mode == "fixed":
            self._running = True
            await asyncio.gather(
                self._run_parse_data_stream(),
                self._run_fixed_downsampler(),
            )
        elif self._mode == "wait-for-set":
            await self._run_wait_for_set_downsampler()
        else:
            raise ValueError(f"Unsupported run mode: {self._mode}")

    async def _run_timestamp_downsampler(self) -> None:
        async for data in self._input_data_stream:
            for cache in self._caches:
                try:
                    if not cache.accepts(data):
                        continue

                    if "t" in data:
                        tstamp = data["t"]
                        qn_split = data["q"].split(".")
                        self._processed_datapoints_metric.add()
                        self._input_data_lag_metric.add(
                            tstamp, exchange=qn_split[0], asset_class=qn_split[1]
                        )

                        if tstamp >= cache.next_run_time:
                            self._calc_lag_metric.add(
                                cache.next_run_time,
                                calc_name=cache.name,
                                stage="start_lag",
                            )

                            self._fire_and_forget_calc(cache.next_run_time, cache)
                            cache.next_run_time = self._get_next_snapshot_timestamp(
                                cache.next_run_time
                            )

                    cache.on_new_data(data)
                except Exception as e:
                    logging.exception(
                        f"Failed to process datapoint for {cache.name} {data=} {e}"
                    )

    async def _run_parse_data_stream(self) -> None:
        async for data in self._input_data_stream:
            for cache in self._caches:
                try:
                    if not cache.accepts(data):
                        continue

                    if "t" in data:
                        timestamp = data["t"]
                        qn_split = data["q"].split(".")
                        self._processed_datapoints_metric.add()
                        self._input_data_lag_metric.add(
                            timestamp, exchange=qn_split[0], asset_class=qn_split[1]
                        )

                    cache.on_new_data(data)
                except Exception as e:
                    logging.exception(
                        f"Failed to process datapoint for {cache.name} {data=} {e}"
                    )
        self._running = False

    async def _run_fixed_downsampler(self) -> None:
        next_snapshot_t = self._get_next_snapshot_start()

        while self._running:
            timestamp = time.time_ns()

            if timestamp >= next_snapshot_t:
                for cache in self._caches:
                    self._calc_lag_metric.add(
                        next_snapshot_t, calc_name=cache.name, stage="start_lag"
                    )
                    self._fire_and_forget_calc(next_snapshot_t, cache)
                next_snapshot_t = self._get_next_snapshot_timestamp(next_snapshot_t)

            await asyncio.sleep((next_snapshot_t - time.time_ns()) / 1e9)

    async def _run_wait_for_set_downsampler(self) -> None:
        recent_fire_ts: dict[int, int] = defaultdict(int)

        if not self._is_wait_for_set_collection():
            raise RuntimeError("Not all caches are WaitForSetCalculationCache")

        caches = cast(Collection[WaitForSetCalculationCache], self._caches)

        async for data in self._input_data_stream:
            for cache in caches:
                try:
                    self._wait_for_set_iteration(recent_fire_ts, data, cache)
                except Exception as e:
                    logging.exception(
                        f"Failed to process datapoint for {cache.name} {data=} {e}"
                    )

    def _wait_for_set_iteration(
        self,
        recent_fire_ts: dict[int, int],
        data: Any,
        cache: WaitForSetCalculationCache,
    ) -> None:
        if not cache.accepts(data):
            return

        qfn_set = cache.get_qfn_set()
        if "t" not in data:
            cache.on_new_data(data)
            return

        new_data_timestamp = data["t"]
        if data["q"] in qfn_set:
            if new_data_timestamp <= recent_fire_ts[id(cache)]:
                return

            log_bsdebug(
                "[set_to_wait_for] Received %s, tstamp=%s",
                data["q"],
                to_iso(new_data_timestamp),
            )
            cache.update_timestamp_to_qfn_map(new_data_timestamp, data["q"])

        qn_split = data["q"].split(".")
        self._processed_datapoints_metric.add()
        self._input_data_lag_metric.add(
            new_data_timestamp,
            exchange=qn_split[0],
            asset_class=qn_split[1],
        )
        cache.on_new_data(data)

        if (
            new_data_timestamp > recent_fire_ts[id(cache)]
            and new_data_timestamp in cache.get_timestamp_to_qfn_map()
            and len(cache.get_timestamp_to_qfn_map()[new_data_timestamp])
            == len(qfn_set)
        ):
            logging.info(
                f"Recieved all qfn's firing for {new_data_timestamp=} ({to_datetime(new_data_timestamp)})"
            )
            recent_fire_ts[id(cache)] = self._fire_and_forget_wait_for_set(
                new_data_timestamp, cache
            )
            return

        for ts_for_set in list(cache.get_timestamp_to_qfn_map().keys()):
            diff = to_datetime(new_data_timestamp) - to_datetime(ts_for_set)

            # Flush older sets through
            if diff > timedelta(seconds=self._get_next_freq() / 1e9):
                logging.warning(
                    f"Firing {ts_for_set=} with the following MISSING qn's {qfn_set - cache.get_timestamp_to_qfn_map()[ts_for_set]}, previously triggered {to_iso(recent_fire_ts[id(cache)])}"
                )
                if ts_for_set > recent_fire_ts[id(cache)]:
                    recent_fire_ts[id(cache)] = self._fire_and_forget_wait_for_set(
                        ts_for_set, cache
                    )
                else:
                    logging.warning(
                        f"Deleting stale incomplete set to wait for {ts_for_set=} missing qn's={qfn_set - cache.get_timestamp_to_qfn_map()[ts_for_set]}, previously triggered={recent_fire_ts[id(cache)]}"
                    )
                    cache.remove_ts_from_qfn_mapping(ts_for_set)
                    # Clean up cache data
                    cache.on_clean_up()

    def _fire_and_forget_wait_for_set(
        self, tstamp: int, cache: WaitForSetCalculationCache
    ) -> int:
        log_bsdebug("starting calc for %d", tstamp)
        self._calc_lag_metric.add(
            tstamp,
            calc_name=cache.name,
            stage="start_lag",
        )
        self._fire_and_forget_calc(
            tstamp,
            cache,
        )
        # track latest fire & remove old timestamp set
        assert cache.get_timestamp_to_qfn_map() is not None
        cache.remove_ts_from_qfn_mapping(tstamp)

        return tstamp

    def _fire_and_forget_calc(
        self, timestamp: int, cache: CalculationCache | WaitForSetCalculationCache
    ) -> None:
        cache_name = cache.name
        if len(self._running_tasks[cache_name]) >= max(
            1, int(NUM_WORKERS / len(self._caches))
        ):
            # This means all workers are still busy running previous calculations.
            # Note: We divide the number of workers per running cache to prevent caches with high calc lag from taking
            # all available workers and affecting others.
            logging.warning(
                f"skipping calculation for cache '{cache.name}', too many task waiting to finish execution"
            )
            return

        cache_data = cache.get_data(copy=self._copy_data_on_calc)
        loop = asyncio.get_running_loop()
        task = loop.create_task(
            self._run_calc_and_put_output(timestamp, cache_name, cache_data)
        )

        # save the reference to prevent garbage collection
        self._running_tasks[cache.name].add(task)

        task.add_done_callback(self._running_tasks[cache.name].discard)

    async def _run_calc_and_put_output(
        self, timestamp: int, cache_name: str, cache_data: dict[str, Any]
    ) -> None:
        lag = time.time() - timestamp / 1e9
        log_bsdebug(
            "Starting calculation for %s timestamp=%d lag=%d.3f",
            cache_name,
            timestamp,
            lag,
        )
        try:
            live_handler = partial(
                _run_calc,
                name=cache_name,
                timestamp=timestamp,
                prep_data=self._prep_data,
                process_chunk_helper=self._process_chunk_helper,
                cache_data=cache_data,
            )
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(self._pool, live_handler)
            if result is None:
                return

            metrics, output = result

            for stage, ts in metrics.items():
                # adding metrics generated by calc execution
                self._calc_lag_metric.add(
                    ts[0], ts[1], calc_name=cache_name, stage=stage
                )

        except (BrokenExecutor, process.BrokenProcessPool) as be:
            logging.exception(be)
            # Severe error, recreate the pool otherwise child processes cannot run
            self._pool.shutdown()
            self._pool = ProcessPoolExecutor(max_workers=NUM_WORKERS)
            return
        except Exception as e:
            logging.exception(
                f"Failed to start calculation {cache_name} {timestamp=} {e}"
            )
            return

        if (isinstance(output, pd.DataFrame) and not output.empty) or (
            not isinstance(output, pd.DataFrame) and output
        ):
            try:
                await self._put_output(self._output_data_stream, output)
                lag = time.time() - timestamp / 1e9
                log_bsdebug(
                    "Output successful for %s timestamp=%d %s.3f",
                    cache_name,
                    timestamp,
                    lag,
                )
                # put_output_lag: from process_data ends until _put_output finish
                self._calc_lag_metric.add(
                    metrics["process_data_lag"][1],
                    calc_name=cache_name,
                    stage="put_output_lag",
                )
            except Exception as e:
                logging.exception(f"Output failed for {cache_name} {timestamp=} {e}")
        elif self._log_empty_output_warning:
            logging.warning(
                f"Output was empty after calc ran: {cache_name}, {timestamp=}, calc input data len:{len(cache_data)}"
            )
        self._calc_lag_metric.add(timestamp, calc_name=cache_name, stage="total_lag")

    def _get_next_freq(self) -> int:
        if isinstance(self._caches, AutoScalingCacheManager):
            freq = self._caches.get_maximum_ns(self._frequency_ns)
        else:
            freq = self._frequency_ns
        return freq

    def _get_next_snapshot_timestamp(self, prev_timestamp: int) -> int:
        """
        Prevents lag accumulation by skipping snapshots between last calculated and most recently expected.
        Effectively caps lag to the frequency value.
        """
        freq = self._get_next_freq()
        naive_next_snapshot_t = prev_timestamp + freq
        most_recent_expected_snapshot_t = self._get_next_snapshot_start() - freq
        return max(naive_next_snapshot_t, most_recent_expected_snapshot_t)

    def _get_next_snapshot_start(self) -> int:
        freq = self._get_next_freq()
        return int(math.ceil(time.time_ns() / freq) * freq)

    def _is_wait_for_set_collection(
        self,
    ) -> bool:
        return all(
            isinstance(cache, WaitForSetCalculationCache) for cache in self._caches
        )
