import asyncio
import inspect
import logging
import time
from collections.abc import A<PERSON><PERSON>, Call<PERSON>, <PERSON><PERSON><PERSON>, Iterator
from typing import (
    Generic,
    TypeGuard,
    TypeVar,
)

from utils_general import json_loads, log_bsdebug

from block_stream.consumer.cache import CalcCaches
from block_stream.typings import EcsTaskInfo
from block_stream.utils.ecs_cluster_manager import EcsClusterManager
from block_stream.utils.locks import LazyInitLock

T = TypeVar("T", bound=Hashable)
CacheT = TypeVar("CacheT", bound=CalcCaches)
GetTargetJobsReturnType = tuple[list[T], int]


def default_remove_cache_fn(caches: dict[T, CacheT], target: T) -> None:
    del caches[target]


class AutoScalingCacheManager(Generic[T, CacheT]):
    """
    Manages Calc Caches (and by proxy) computations assigned to an ECS instance
    through auto-scaling events.

    Note: data_set must be hashable type!

    Intended usage is at the top level of calcs:

    ```
    scaling_cache = AutoScalingCacheManager(
        target_jobs=data_sets,
        create_cache_fn=_get_calc_cache
    )

    consumer = Consumer(
        ...
        caches=scaling_cache
    )

    await asyncio.run(consumer.async_run())
    """

    def __init__(
        self,
        target_jobs: list[T],
        create_cache_fn: Callable[[T], CacheT | Awaitable[CacheT]],
        remove_cache_fn: Callable[[dict[T, CacheT], T], None] = default_remove_cache_fn,
        check_frequency_s: int = 60,
        data_set_freq_key: str = "output_frequency",
        name: str = "cache",
        allow_dynamic_target_jobs: bool = False,
        target_job_reloader: Callable[[], GetTargetJobsReturnType[T]] | None = None,
    ):
        """
        :param target_jobs: List of target jobs that will be considered by the manager when scaling
        :param create_cache_fn: Custom Async function to initialize a new cache
        :param remove_cache_fn: Custom function to remove a running cache
        :param check_frequency_s: Frequency in seconds to check if rebalance is needed
        :param data_set_freq_key: Name of the key inside target_job to get the frequency of the Cache.
        :param name: Name of the cache (use by the consumer)
        :param allow_dynamic_target_jobs: Specify if target_jobs list could change during execution. Default: False.
        """
        self._target_jobs: list[T] = target_jobs
        self._target_job_reloader = target_job_reloader
        self._check_frequency_s = check_frequency_s
        self._data_set_freq_key = data_set_freq_key
        self._name = name
        self._cache_output_frequency_ns: int | None = None
        self._ecs_manager = EcsClusterManager()

        # Functions to create/remove caches based on target definitions
        self._create_cache_fn = create_cache_fn
        self._remove_cache_fn = remove_cache_fn

        self._ecs_meta_cache: EcsTaskInfo | None = None
        self._caches: dict[T, CacheT] = {}

        # If True then we assume target_jobs list could change
        self._allow_dynamic_target_jobs = allow_dynamic_target_jobs
        self._rebalance_cache_lock = LazyInitLock()

    async def _get_new_targets(self) -> tuple[set[T], set[T]]:
        ecs_meta = await self._ecs_manager.get_task_info()
        if ecs_meta == self._ecs_meta_cache and not self._allow_dynamic_target_jobs:
            return set(), set()

        self._ecs_meta_cache = ecs_meta

        # Sort target jobs to ensure consistent distribution across tasks
        self._target_jobs.sort()
        targets = {
            target
            for idx, target in enumerate(self._target_jobs)
            if idx % ecs_meta["running_task_count"] == ecs_meta["current_task_index"]
        }

        retired_targets: set[T] = set(self._caches.keys()) - targets
        new_targets: set[T] = targets - set(self._caches.keys())
        return new_targets, retired_targets

    def _is_cache_instance(
        self, value: CacheT | Awaitable[CacheT]
    ) -> TypeGuard[CacheT]:
        """Type guard to check if value is a cache instance (not awaitable or is a Task)."""
        return not inspect.isawaitable(value) or isinstance(value, asyncio.Task)

    def _is_awaitable_cache(
        self, value: CacheT | Awaitable[CacheT]
    ) -> TypeGuard[Awaitable[CacheT]]:
        """Type guard to check if value needs to be awaited."""
        return inspect.isawaitable(value) and not isinstance(value, asyncio.Task)

    async def _create_cache(self, target: T) -> CacheT:
        """
        Create a cache for the given target, handling both sync and async creation functions.
        """
        result = self._create_cache_fn(target)

        if self._is_awaitable_cache(result):
            # It's an awaitable that needs to be awaited
            return await result
        elif self._is_cache_instance(result):
            # It's already a cache instance
            return result
        else:
            # This should never happen given our type guards, but satisfies mypy
            raise TypeError(f"Unexpected result type: {type(result)}")

    async def rebalance_caches(self) -> None:
        async with self._rebalance_cache_lock:
            # This is split as type annotation is required and unpacking cannot be combined
            # with type annotation
            targets: tuple[set[T], set[T]] = await self._get_new_targets()
            new_targets, retired_targets = targets

            # Skip if there is nothing to rebalance
            if not new_targets and not retired_targets:
                return
            logging.info(f"Rebalancing {self._name}: {new_targets=} {retired_targets=}")

            # Removing existing Caches first to avoid spiking resource use. This may not be the
            # right solution (as it could cause a gap in data while scaling). However, the concern
            # is that otherwise tasks are just going to fall over during scaling
            for target in retired_targets:
                logging.info(f"Removing {self._name} (rebalancing): {target}")
                self._remove_cache_fn(self._caches, target)

            if new_targets:
                cache_results: dict[T, CacheT] = {}

                async def create_cache_with_timing(target: T) -> None:
                    try:
                        logging.info(f"Creating new {self._name}: {target}")
                        t = time.time()
                        new_cache = await self._create_cache(target)
                        logging.info(
                            f"New {self._name} created in {time.time()-t:.3f}s: {target}"
                        )
                        cache_results[target] = new_cache
                    except Exception as e:
                        logging.error(f"Failed to create cache for {target}: {e}")

                async with asyncio.TaskGroup() as tg:
                    for target in new_targets:
                        tg.create_task(create_cache_with_timing(target))

                if cache_results:
                    if self._caches:
                        # Assert same instance of cache so user cannot mix WaitForSetCaches with standard calc caches
                        expected_type = type(next(iter(self._caches.values())))
                        for new_cache in cache_results.values():
                            assert isinstance(new_cache, expected_type)
                    else:
                        types_in_batch = {type(c) for c in cache_results.values()}
                        assert (
                            len(types_in_batch) <= 1
                        ), f"Inconsistent cache types in batch: {types_in_batch}"

                    self._caches.update(cache_results)
                    logging.info(f"Updated {self._name} caches: {self._caches=}")

            new_freq: int | None = None
            for cache in self._caches.keys():
                try:
                    json = json_loads(cache)
                    output_freq: int | None = json.get(self._data_set_freq_key)
                    if output_freq and (not new_freq or output_freq > new_freq):
                        new_freq = output_freq

                except Exception as e:
                    logging.warning(
                        f"[AutoScalingCacheManager] Unable to set {self._name} output frequency on rebalance, "
                        f"could not find key {self._data_set_freq_key} in target {e}"
                    )
            if self._cache_output_frequency_ns != new_freq:
                logging.info(
                    f"Total number of {self._name}s running {len(self._caches.keys())}  "
                    f"(Frequency(s): {(new_freq / 1e9) if new_freq else 'Undefined, using default from consumer'})"
                )
                self._cache_output_frequency_ns = new_freq

    async def reload_target_jobs_worker(
        self,
        reload_freq_sec: float = 3600,  # Every hour
    ) -> None:
        """
        Providers an async watcher to reload blockstream targets.

            :param reload_freq_sec: How often to refetch the config and check for updates.

            :return: None
        """
        next_iter = int((time.time() // reload_freq_sec) * reload_freq_sec)
        assert (
            self._target_job_reloader
        ), "Define target_job_reloader fn when instantiating class"
        while True:
            try:
                next_iter += int(reload_freq_sec)
                sleep_time = next_iter - time.time()
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

                logging.info("Reloading cache manager's config")

                new_target_jobs, _ = self._target_job_reloader()

                self._target_jobs.clear()
                self._target_jobs.extend(new_target_jobs)

                # Run rebalance caches
                await self.rebalance_caches()

            except Exception as ex:
                logging.exception(
                    f"Failed to reload {self._name} cache manager's target jobs: {ex} {self._target_jobs=}"
                )

    async def async_run(self) -> None:
        """
        Worker process to periodically rebalance caches (if needed). This is automatically
        called by the Consumer.

        :return: N/A
        """

        logging.info("Starting AutoScalingCacheManager")
        while True:
            try:
                await self.rebalance_caches()
            except Exception as e:
                logging.exception(f"Failed to rebalance {self._name}s: {e}")

            # Wait outside try/except to ensure no overdrive on error
            next_check = (
                time.time() // self._check_frequency_s + 1
            ) * self._check_frequency_s
            await asyncio.sleep(next_check - time.time())

    def get_maximum_ns(self, default_ns: int) -> int:
        """
        In the event that a task is running multiple Caches/data sets this function
        returns the maximum run time given by a data set in nano seconds.
        """
        if not self._cache_output_frequency_ns:
            log_bsdebug("[AutoScalingCacheManager] Using default_ns %d", default_ns)
            return default_ns
        return self._cache_output_frequency_ns

    def __iter__(self) -> Iterator[CacheT]:
        return self._caches.values().__iter__()

    def __len__(self) -> int:
        return len(self._caches)
