import copy as cp
import logging
import time
from abc import ABC, abstractmethod
from collections.abc import Callable, Mapping
from datetime import UTC, datetime, timedelta
from functools import reduce
from typing import Any, cast

from utils_general import from_iso, lazy_log_bsdebug, to_datetime

from block_stream.consumer.typings import (
    CalcCacheData,
    CalculationCacheException,
    CalculatorCacheInput,
    CalculatorCacheOutput,
    QualifiedNameToLastTick,
    TickCacheData,
    TickDataCacheRawData,
    TimeStampToTickData,
)
from block_stream.typings import (
    CatalogData,
    CatalogFilter,
    InstrumentDetails,
    InstrumentsDetailsMap,
)


class Cache(ABC):
    """
    Generic cache interface.
    """

    def __init__(self) -> None:
        self._next_run_time: int | None = None

    @property
    def next_run_time(self) -> int:
        if self._next_run_time is None:
            logging.error("next_run_time not set for cache. Setting to now.")
            self._next_run_time = time.time_ns()
        return self._next_run_time

    @next_run_time.setter
    def next_run_time(self, value: int) -> None:
        self._next_run_time = value

    def _log_expired_instruments(self, expired_instruments: set[str]) -> None:
        if not expired_instruments:
            return
        lazy_log_bsdebug(
            "Removing expired instruments from cache: %s",
            lambda: ", ".join(expired_instruments),
        )

    @abstractmethod
    def get_data(self, copy: bool = True) -> Mapping[str, Any]:
        """
        Returns the cache content as dictionary of named parameters (kwargs).
        :params deepcopy: If True makes a copy of the content of the cache.
        """
        pass

    @abstractmethod
    def accepts(self, data: Any) -> bool:
        """
        :returns: True if the cache accepts the object `data`, False otherwise.
        """
        pass

    @abstractmethod
    def on_clean_up(self) -> None:
        """
        Called periodically to clean up old data.
        """
        pass

    @abstractmethod
    def on_new_data(self, data: Any) -> bool:
        """
        :returns: True if the cache stores the object `data`, False otherwise.
        """
        pass


class CalculationCache(Cache):
    """
    Cache containing all the data needed for a calculation.
    """

    def __init__(
        self,
        caches: list[Cache],
        name: str,
        generate_dynamic_params: Callable[[], dict[str, Any]] | None = None,
        **kwargs: Any,
    ) -> None:
        """
        :params caches: Caches to be used for the calculation.
        :params name: Name used in the consumer when logging messages from the calculation.
                      Necessary to distinguish errors between multiple concurrent calculations.
        :params generate_dynamic_params: A callable that returns a dictionary of dynamically changing
                      parameters for each function execution e.g. datetimes.
        :params kwargs: Named parameters to be passed to the prep_data() and process_data() functions.
        """
        self._caches = caches
        self._kwargs = kwargs
        self.name = name
        self._generate_dynamic_params = generate_dynamic_params

    def get_data(self, copy: bool = True) -> dict[str, Any]:
        """
        Merges the data of all caches.
        """

        def merge(result: dict[str, object], cache: Cache) -> dict[str, object]:
            for k, v in cache.get_data(copy).items():
                if k not in result:
                    result[k] = v if copy else cp.copy(v)
                else:
                    if isinstance(result[k], list):
                        cast(list[Any], result[k]).extend(v)
                    elif isinstance(result[k], dict) and k == "instruments":
                        cast(dict[str, Any], result[k]).update(v)
                    else:
                        raise CalculationCacheException(
                            f"Found {k} in another cache but it cannot be merged"
                        )

            return result

        init: dict[str, Any] = {**self._kwargs}

        if self._generate_dynamic_params:
            params = self._generate_dynamic_params()
            init.update(params)

        return reduce(merge, self._caches, init)

    def accepts(self, data: Any) -> bool:
        return any(c.accepts(data) for c in self._caches)

    def on_clean_up(self) -> None:
        for cache in self._caches:
            cache.on_clean_up()

    def on_new_data(self, data: Any) -> bool:
        result = [c.on_new_data(data) for c in self._caches]
        return any(result)


class WaitForSetCalculationCache(CalculationCache):
    def __init__(
        self,
        caches: list[Cache],
        name: str,
        generate_dynamic_params: Callable[[], dict[str, Any]] | None = None,
        set_to_wait_for: set[str] | None = None,
        timestamp_to_qfn_map: dict[int, set[str]] | None = None,
        **kwargs: Any,
    ) -> None:
        """
        All params defined in CalculationCache, apart from attaches information about the qualified names to
        wait for, as well as the keyed timestamps used in the consumer.

        :params set_to_wait_for: Optionally provide the qualified names to wait for, if given in the Consumer
                      this will be ignored. Good for use with auto scaling manager to determine the overall set.
        :params kwargs: Named parameters to be passed to the prep_data() and process_data() functions.
        """
        super().__init__(
            caches,
            name,
            generate_dynamic_params,
            **kwargs,
        )
        if not set_to_wait_for:
            raise ValueError(
                "Please provide a set of qfns via set_to_wait_for when running a WaitForSetCalculationCache."
            )
        if not timestamp_to_qfn_map:
            timestamp_to_qfn_map = {}
        self._set_to_wait_for = set_to_wait_for
        self._timestamp_to_qfn_map = timestamp_to_qfn_map

    def get_qfn_set(self) -> set[str]:
        return self._set_to_wait_for

    def get_timestamp_to_qfn_map(self) -> dict[int, set[str]]:
        return self._timestamp_to_qfn_map

    def update_timestamp_to_qfn_map(self, timestamp: int, qualified_name: str) -> None:
        if timestamp not in self._timestamp_to_qfn_map:
            self._timestamp_to_qfn_map[timestamp] = {qualified_name}
        else:
            self._timestamp_to_qfn_map[timestamp].add(qualified_name)

    def remove_ts_from_qfn_mapping(self, timestamp: int) -> None:
        if self._timestamp_to_qfn_map and timestamp in self._timestamp_to_qfn_map:
            del self._timestamp_to_qfn_map[timestamp]


class LastTickCache(Cache):
    _instruments: InstrumentsDetailsMap

    def __init__(
        self,
        predicate_fn: Callable[[Any, InstrumentsDetailsMap], bool] | None = None,
        prefetched_ticks: QualifiedNameToLastTick | None = None,
        prefetched_instruments: InstrumentsDetailsMap | None = None,
        catalog_filters: list[CatalogFilter] | None = None,
        copy_new_data: bool = False,
    ) -> None:
        """
        :param predicate_fn: Custom function that given a datapoint returns True if it should be accepted or False otherwise.
        :param catalog_filters: A dictionary containing lists of which catalog updates to listen to,
        if provided predicate_fn will not be used as ticks will be checked against the instrument map.
        :param copy_new_data: Enable/disable copy of new incoming data.
        """
        if predicate_fn is None and catalog_filters is None:
            logging.warning(
                "No predicate_fn or catalog_filters set, allowing all ticks."
            )
            predicate_fn = _default_predicate_fn
        if predicate_fn and catalog_filters:
            logging.warning(
                "Predicate_fn provided to LastTickCache will be superseded by catalog_filters"
            )
        self._predicate_fn = predicate_fn
        self._catalog_filters = catalog_filters
        self._ticks = prefetched_ticks if prefetched_ticks else {}
        self._instruments = prefetched_instruments if prefetched_instruments else {}
        self._copy_new_data = copy_new_data

    def get_data(self, copy: bool = True) -> TickCacheData:
        ticks = list(self._ticks.values())
        instruments = cp.copy(self._instruments) if copy else self._instruments
        return {
            "raw_data": ticks,
            "instruments": instruments,
        }

    def accepts(self, data: Any) -> bool:
        if not _is_valid_tick(data):
            return False

        if self._catalog_filters:
            return (
                _allow_catalog_update(data, self._catalog_filters)
                if data["q"].endswith(".contracts")
                else _filter_data(data, self._catalog_filters, self._instruments)
            )

        return callable(self._predicate_fn) and self._predicate_fn(
            data, self._instruments
        )

    def on_clean_up(self) -> None:
        expired_instruments = _get_expired_instruments(self._instruments)

        if not expired_instruments:
            return
        self._log_expired_instruments(expired_instruments)
        self._instruments = {
            k: v for k, v in self._instruments.items() if k not in expired_instruments
        }

        original_data_len = len(self._ticks)
        self._ticks = {
            k: v
            for k, v in self._ticks.items()
            if _is_tick_preserved(v, expired_instruments)
        }
        lazy_log_bsdebug(
            "Removed %d ticks from cache",
            lambda: original_data_len - len(self._ticks),
        )

    def on_new_data(self, data: Any) -> bool:
        if not self.accepts(data):
            return False

        if data["q"].endswith(".contracts"):
            catalog_data = cast(CatalogData, data)
            self._on_new_instrument(catalog_data)
        else:
            self._on_new_tick(data)

        return True

    def _on_new_instrument(self, instrument: CatalogData) -> None:
        qn_split = instrument["q"].split(".")[0:2]
        key = f"{qn_split[0]}.{qn_split[1]}.{instrument['instrument']}"
        self._instruments[key] = _convert_to_instrument_details(
            instrument, self._copy_new_data
        )

    def _on_new_tick(self, tick: Any) -> None:
        qualified_name = tick["q"]
        self._ticks[qualified_name] = _convert_block_stream_tick(
            tick, self._copy_new_data
        )


class HistoricalTickCache(Cache):
    def __init__(
        self,
        time_window: timedelta,
        predicate_fn: Callable[[Any, InstrumentsDetailsMap], bool] | None = None,
        prefetched_ticks: list[Any] | None = None,
        prefetched_instruments: InstrumentsDetailsMap | None = None,
        catalog_filters: list[CatalogFilter] | None = None,
        copy_new_data: bool = False,
    ) -> None:
        """
        :param time_window: Defines the duration of data retention. The cache may temporarily store datapoints that are outside this time window.
        :param predicate_fn: Custom function that given a datapoint returns True if it should be accepted or False otherwise.
        :param catalog_filters: A dictionary containing lists of which catalog updates to listen to,
        if provided predicate_fn will not be used as ticks will be checked against the instrument map.
        :param copy_new_data: Enable/disable copy of new incoming data.
        """
        if predicate_fn is None and catalog_filters is None:
            logging.warning(
                "No predicate_fn or catalog_filters set, allowing all ticks."
            )
            predicate_fn = _default_predicate_fn
        if predicate_fn and catalog_filters:
            logging.warning(
                "Predicate_fn provided to HistoricalTickCache will be superseeded by catalog_filters"
            )

        self._predicate_fn = predicate_fn
        self._catalog_filters = catalog_filters
        self._time_window = time_window
        self._ticks = prefetched_ticks if prefetched_ticks else []
        self._instruments = prefetched_instruments if prefetched_instruments else {}
        self._copy_new_data = copy_new_data

    def get_data(self, copy: bool = True) -> TickCacheData:
        return {
            "raw_data": cp.copy(self._ticks) if copy else self._ticks,
            "instruments": cp.copy(self._instruments) if copy else self._instruments,
        }

    def accepts(self, data: Any) -> bool:
        if not _is_valid_tick(data):
            return False

        if self._catalog_filters:
            return (
                _allow_catalog_update(data, self._catalog_filters)
                if data["q"].endswith(".contracts")
                else _filter_data(data, self._catalog_filters, self._instruments)
            )

        return callable(self._predicate_fn) and self._predicate_fn(
            data, self._instruments
        )

    def on_clean_up(self) -> None:
        expired_instruments = _get_expired_instruments(self._instruments)
        old_timestamp = time.time_ns() - int(self._time_window.total_seconds() * 1e9)

        if expired_instruments:
            self._log_expired_instruments(expired_instruments)
            self._instruments = {
                k: v
                for k, v in self._instruments.items()
                if k not in expired_instruments
            }

        original_data_len = len(self._ticks)
        self._ticks = [
            tick
            for tick in self._ticks
            if _is_tick_preserved(tick, expired_instruments, old_timestamp)
        ]
        lazy_log_bsdebug(
            "Removed %d ticks from cache",
            lambda: original_data_len - len(self._ticks),
        )

    def on_new_data(self, data: Any) -> bool:
        if not self.accepts(data):
            return False

        if data["q"].endswith(".contracts"):
            catalog_data = cast(CatalogData, data)
            self._on_new_instrument(catalog_data)
        else:
            self._on_new_tick(data)

        return True

    def _on_new_instrument(self, instrument: CatalogData) -> None:
        qn_split = instrument["q"].split(".")[0:2]
        key = f"{qn_split[0]}.{qn_split[1]}.{instrument['instrument']}"
        self._instruments[key] = _convert_to_instrument_details(
            instrument, self._copy_new_data
        )

    def _on_new_tick(self, tick: Any) -> None:
        self._ticks.append(_convert_block_stream_tick(tick, self._copy_new_data))


class DerivedDataCache(Cache):
    """
    Cache interface to store the latest derived data, given a set of qualified names.
    """

    def __init__(
        self,
        static_qualified_names: set[str] | None = None,
        predicate_fn: Callable[[Any], bool] | None = None,
        cleanup_fn: None | (
            Callable[
                [QualifiedNameToLastTick | TimeStampToTickData],
                QualifiedNameToLastTick,
            ]
        ) = None,
        prefetched_datapoints: None | (
            QualifiedNameToLastTick | TimeStampToTickData
        ) = None,
        copy_new_data: bool = False,
        store_by_timestamp: bool = False,
        cleanup_secs: int | None = None,
        validate_latest_timestamp: bool = False,
    ) -> None:
        """
        If both static_qualified_names and predicate_fn are provided, the Cache will first check with the
        static_qualified_names set and then with the predicate_fn to accept a data point.

        :param static_qualified_names: Set of acceptable static qualified names to listen too.
        :param predicate_fn: Custom function that given a datapoint returns True if it should be accepted
        or False otherwise.
        :param cleanup_fn: Custom cleanup function designed to remove expired or unused data from the datapoints map
        :param prefetched_datapoints: Preloaded derived data from the database.
        :param copy_new_data: Enable/disable copy of new incoming data.
        :param store_by_timestamp: Nests the derived data by storing with timestamp and then qfns
        :param cleanup_secs: seconds we allow older timestamps to be stored for, only used if store_by_timestamp is True
        :param validate_latest_timestamp: Enable validation to ensure the latest data point is stored in the cache.
        """
        if predicate_fn is None and static_qualified_names is None:
            logging.error(
                "DerivedDataCache should be initialized with a set of qualified names or a predicate function to "
                "check incoming data against; all datapoints will be rejected."
            )
            static_qualified_names = set()

        if not store_by_timestamp and cleanup_secs:
            logging.warning(
                "cleanup_secs is redundant and only used when data keyed by timestamp"
            )

        if store_by_timestamp and not cleanup_secs:
            cleanup_secs = 30

        self._predicate_fn = predicate_fn
        self._static_qualified_names = static_qualified_names
        self._cleanup_fn = cleanup_fn
        self._datapoints = prefetched_datapoints if prefetched_datapoints else {}
        self._copy_new_data = copy_new_data
        self._store_by_timestamp = store_by_timestamp
        self._cleanup_secs = cleanup_secs
        self._validate_latest_timestamp = validate_latest_timestamp

    def get_data(self, copy: bool = True) -> TickCacheData:
        params_list: TickDataCacheRawData = []
        if self._store_by_timestamp:
            for _, nested_dict in self._datapoints.items():
                for _, params in nested_dict.items():
                    params_list.append(params)
            return {"raw_data": params_list}
        return {
            "raw_data": list(self._datapoints.values()),
        }

    def accepts(self, data: Any) -> bool:
        return (
            self._static_qualified_names
            and ("q" in data)
            and ("t" in data)
            and (data["q"] in self._static_qualified_names)
        ) or (callable(self._predicate_fn) and self._predicate_fn(data))

    def on_clean_up(self) -> None:
        if callable(self._cleanup_fn):
            self._datapoints = self._cleanup_fn(self._datapoints)
        elif self._store_by_timestamp:
            self._datapoints = cast(TimeStampToTickData, self._datapoints)
            self._cleanup_secs = cast(int, self._cleanup_secs)

            for ts, _ in list(self._datapoints.items()):
                delta = datetime.now(UTC) - to_datetime(ts)
                if delta > timedelta(seconds=self._cleanup_secs * 2):
                    del self._datapoints[ts]

    def on_new_data(self, data: Any) -> bool:
        if not self.accepts(data):
            return False

        self._on_new_tick(data)
        return True

    def _on_new_tick(self, tick: Any) -> None:
        qualified_name = tick["q"]
        if self._store_by_timestamp:
            if tick["t"] not in self._datapoints:
                self._datapoints[tick["t"]] = {}

            self._datapoints[tick["t"]][qualified_name] = _convert_block_stream_tick(
                tick, self._copy_new_data
            )
        else:
            if (
                self._validate_latest_timestamp
                and qualified_name in self._datapoints
                and self._datapoints[qualified_name]["timestamp"] > tick["t"]
            ):
                # Ignore older ticks
                return

            self._datapoints[qualified_name] = _convert_block_stream_tick(
                tick, self._copy_new_data
            )


class TickCalcCache(Cache):
    """
    This cache will store calculated data to be used by the Streaming Calculator.
    It will process each datapoint and execute the specified calc function.
    """

    def __init__(
        self,
        tick_calc_fn: Callable[
            [Any, CalculatorCacheInput, CalculatorCacheOutput, InstrumentsDetailsMap],
            CalculatorCacheOutput,
        ],
        predicate_fn: Callable[[Any, InstrumentsDetailsMap], bool] | None = None,
        cleanup_fn: None | (
            Callable[
                [CalculatorCacheInput, CalculatorCacheOutput, set[str]],
                tuple[CalculatorCacheInput, CalculatorCacheOutput],
            ]
        ) = None,
        preloaded_calc_input: CalculatorCacheInput | None = None,
        preloaded_calc_output: CalculatorCacheOutput | None = None,
        prefetched_instruments: InstrumentsDetailsMap | None = None,
        catalog_filters: list[CatalogFilter] | None = None,
        copy_new_data: bool = False,
        return_instruments: bool = True,
    ) -> None:
        """
        :param tick_calc_fn: Custom function that will process each tick.
        This function is meant to be used for iterative calculations, and it is expected to store intermediate
        results (or its own output) to be used as input again on the next iteration.
        Both the input and the output are overwritten on each iteration (using the same object reference)
        Input:
         - Last tick datapoint accepted by the Cache
         - CalculatorCacheInput that contains the inputs required by tick_calc_fn.
         - CalculatorCacheOutput of previous calculated results.
         - The instrument list.
         Output:
        - CalculatorCacheOutput of calculated results, this will overwrite the previous output
        :param predicate_fn: Custom function that given a datapoint returns True if it should be accepted or False otherwise.
        :param cleanup_fn: Custom cleanup function designed to remove expired or unused data from both calc_input and calc_output
        :param preloaded_calc_input: Optional. Preloaded input data to initialize the cache.
        :param preloaded_calc_output: Optional. Preloaded calculated output to initialize the cache.
        :param prefetched_instruments: Optional. Pre-fetched instrument list.
        :param catalog_filters: A dictionary containing lists of which catalog updates to listen to,
        if provided predicate_fn will not be used as ticks will be checked against the instrument map.
        :param copy_new_data: Enable/disable copy of new incoming data.
        :param return_instruments: Enable/disable returning the instrument list when get_data is called.
        """
        if predicate_fn is None and catalog_filters is None:
            logging.warning(
                "No predicate_fn or catalog_filters set, allowing all ticks."
            )
            predicate_fn = _default_predicate_fn
        if predicate_fn and catalog_filters:
            logging.warning(
                "Predicate_fn provided to HistoricalTickCache will be superseeded by catalog_filters"
            )

        self._predicate_fn = predicate_fn
        self._catalog_filters = catalog_filters
        self._tick_calc_fn = tick_calc_fn
        self._cleanup_fn = cleanup_fn
        self._calc_input = preloaded_calc_input if preloaded_calc_input else {}
        self._calc_output = preloaded_calc_output if preloaded_calc_output else {}
        self._instruments = prefetched_instruments if prefetched_instruments else {}
        self._copy_new_data = copy_new_data
        self._return_instruments = return_instruments

    def get_data(self, copy: bool = True) -> CalcCacheData:
        data: CalcCacheData = {
            "calculated_data": cp.copy(self._calc_output) if copy else self._calc_output
        }

        if self._return_instruments:
            data["instruments"] = (
                cp.copy(self._instruments) if copy else self._instruments
            )

        return data

    def accepts(self, data: Any) -> bool:
        if not _is_valid_tick(data):
            return False

        if self._catalog_filters:
            return (
                _allow_catalog_update(data, self._catalog_filters)
                if data["q"].endswith(".contracts")
                else _filter_data(data, self._catalog_filters, self._instruments)
            )

        return callable(self._predicate_fn) and self._predicate_fn(
            data, self._instruments
        )

    def on_clean_up(self) -> None:
        expired_instruments = _get_expired_instruments(self._instruments)

        if expired_instruments:
            self._log_expired_instruments(expired_instruments)
            for k in expired_instruments:
                if k in self._instruments:
                    del self._instruments[k]

        if callable(self._cleanup_fn):
            self._calc_input, self._calc_output = self._cleanup_fn(
                self._calc_input, self._calc_output, expired_instruments
            )

    def _on_new_instrument(self, instrument: CatalogData) -> None:
        qn_split = instrument["q"].split(".")[0:2]
        key = f"{qn_split[0]}.{qn_split[1]}.{instrument['instrument']}"
        self._instruments[key] = _convert_to_instrument_details(
            instrument, self._copy_new_data
        )

    def _on_new_tick(self, tick: Any) -> None:
        self._calc_output = self._tick_calc_fn(
            _convert_block_stream_tick(tick, self._copy_new_data),
            self._calc_input,
            self._calc_output,
            self._instruments,
        )

    def on_new_data(self, data: Any) -> bool:
        if not self.accepts(data):
            return False

        if data["q"].endswith(".contracts"):
            catalog_data = cast(CatalogData, data)
            self._on_new_instrument(catalog_data)
        else:
            self._on_new_tick(data)

        return True


def _filter_data(
    data: Any, filters: list[CatalogFilter], instruments: InstrumentsDetailsMap
) -> bool:
    qn_tokens = data["q"].split(".")
    key = ".".join(qn_tokens[:3])

    return key in instruments and any(
        qn_tokens[0] in filter_dict["exchanges"]
        and qn_tokens[1] in filter_dict["asset_class"]
        and instruments[key]["baseAsset"] in filter_dict["base_assets"]
        and (
            not filter_dict.get("quote_assets")
            or instruments[key]["quoteAsset"] in filter_dict["quote_assets"]
        )
        and (
            not filter_dict.get("suffixes")
            or data["q"].endswith(tuple(filter_dict["suffixes"]))
        )
        for filter_dict in filters
    )


def _allow_catalog_update(
    catalog_data: CatalogData, catalog_filters: list[CatalogFilter]
) -> bool:
    if not catalog_filters:
        raise LookupError(
            "catalog_filters should be defined when calling allow_catalog_update"
        )
    qn_tokens = catalog_data["q"].split(".")
    for catalog_filter in catalog_filters:
        if (
            qn_tokens[0] in catalog_filter["exchanges"]
            and qn_tokens[1] in catalog_filter["asset_class"]
            and catalog_data["baseAsset"] in catalog_filter["base_assets"]
            and (
                "quote_assets" not in catalog_filter  # quote_assets optional
                or catalog_data["quoteAsset"] in catalog_filter.get("quote_assets", [])
            )
        ):
            return True
    return False


def _convert_to_instrument_details(
    catalog_data: CatalogData, copy_data: bool
) -> InstrumentDetails:
    if copy_data:
        catalog_data = cp.copy(catalog_data)
    catalog_data = cp.copy(catalog_data)
    converted = cast(dict[str, object], catalog_data)
    converted["qualified_name"] = converted["q"]
    converted["instrument_name"] = converted["instrument"]
    del converted["q"]
    return cast(InstrumentDetails, converted)


def _convert_block_stream_tick(tick: Any, copy_data: bool) -> Any:
    if copy_data:
        tick = cp.copy(tick)
    value_label = str(tick["q"]).split(".")[-1]
    tick["qualified_name"] = tick["q"]
    tick["timestamp"] = tick["t"]
    tick[value_label] = (
        tick["v"] if "v" in tick else tick["p"]
    )  # Model params outputted on p
    del tick["q"]
    del tick["t"]
    if "v" in tick:
        del tick["v"]
    if "p" in tick:
        del tick["p"]
    return tick


def _get_expired_instruments(instruments: InstrumentsDetailsMap) -> set[str]:
    expired_instruments: set[str] = {
        key
        for key, ref in instruments.items()
        if "expiry" in ref and from_iso(ref["expiry"]) < datetime.now(tz=UTC)
    }
    return expired_instruments


def _is_valid_tick(data: Any) -> bool:
    return "q" in data and (("t" in data) or ("instrument" in data))


def _is_tick_preserved(
    tick: Any, expired_instruments: set[str], old_timestamp: int | None = None
) -> bool:
    if old_timestamp and tick["timestamp"] < old_timestamp:
        return False

    return not any(
        tick["qualified_name"].startswith(key) for key in expired_instruments
    )


def _default_predicate_fn(data: Any, instruments: InstrumentsDetailsMap) -> bool:
    return True


# Defined here to stop circular dependencies
CalcCaches = CalculationCache | WaitForSetCalculationCache
