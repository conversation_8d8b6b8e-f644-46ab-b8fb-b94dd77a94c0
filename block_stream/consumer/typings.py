from collections.abc import Callable, Coroutine
from typing import Any, Literal, NotRequired, TypedDict

import pandas as pd

from block_stream.channel import Channel
from block_stream.typings import InstrumentsDetailsMap

QualifiedNameToLastTick = dict[str, Any]
TimeStampToTickData = dict[int, QualifiedNameToLastTick]
CalculationOutput = list[Any] | pd.DataFrame
CalculatorCacheInput = dict[str, Any]
CalculatorCacheOutput = dict[str, Any]


PrepData = Callable[..., Any]
ProcessData = Callable[..., list[Any] | pd.DataFrame]
PutOutput = Callable[[Channel, list[Any] | pd.DataFrame], Coroutine[Any, Any, None]]
ConsumerMode = Literal["fixed", "wait-for-set", "timestamp"]


class CalculationCacheException(Exception):
    pass


TickDataCacheRawData = list[Any]


class TickCacheData(TypedDict):
    raw_data: TickDataCacheRawData
    instruments: NotRequired[InstrumentsDetailsMap]


class CalcCacheData(TypedDict):
    calculated_data: CalculatorCacheOutput
    instruments: NotRequired[InstrumentsDetailsMap]


WaitForSetMapping = dict[int, set[str]]
