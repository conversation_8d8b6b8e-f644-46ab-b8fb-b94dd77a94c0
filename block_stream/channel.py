import asyncio
import logging
import time
from collections import defaultdict
from collections.abc import As<PERSON><PERSON><PERSON><PERSON>, Callable, Iterable
from datetime import datetime
from typing import Any

from utils_general import log_bsdebug

from block_stream.checkpointer import Checkpointer
from block_stream.config import KINESIS_UPDATE_OUTPUT_STREAMS_SEC
from block_stream.kinesis import <PERSON><PERSON><PERSON>
from block_stream.typings import CONSUMER_MODE, OUTPUT_MODE
from block_stream.utils.locks import LazyInitLock


class Channel:
    def __init__(
        self,
        name: str,
        app_name: str,
        shard_index: int,
        seconds_per_checkpoint: int | None,
        custom_stream: str | None,
        endpoint: str | None,
        auto_flush_s: float | None,
        consumer_mode: CONSUMER_MODE,
        output_mode: OUTPUT_MODE,
        start_timestamp: datetime | None,
        start_sequence_number: str | None = None,
        auto_flush_num_records: int | None = 5000,  # Set to None to disable.
        list_streams_interval_secs: None | (
            int
        ) = KINESIS_UPDATE_OUTPUT_STREAMS_SEC,  # Set to None to disable, default 1 day
        output_stream_names_filter: Callable[[str], bool] | None = None,
        input_stream_index: int | None = None,
        enable_backup_input_stream: bool = True,
        include_sequence_number: bool = False,
    ):
        """
        Handles a single channel (both read/write)
        """

        self.name = name
        self.app_name = app_name
        self.shard_index = shard_index
        self._endpoint = endpoint
        self._auto_flush_s = auto_flush_s
        self._put_lock = LazyInitLock()
        self._start_timestamp = start_timestamp
        self._start_sequence_number = start_sequence_number
        # we don't want to list the streams in single mode
        self._list_streams_interval_secs = (
            list_streams_interval_secs if output_mode != "single" else None
        )
        if custom_stream:
            stream_name = custom_stream
        else:
            stream_name = f"channel_{name}"

        endpoint_url: str | None = None
        checkpointer: Checkpointer | None = None
        if endpoint:
            # These are used for local testing
            endpoint_url = f"http://{endpoint}:4567"
            checkpointer = Checkpointer(
                consumer_key=self._get_consumer_key(),
                endpoint_url=f"http://{endpoint}:8000",
            )
            logging.info(f"Channel {name} endpoint: {endpoint}")

        self._transport_layer = Kinesis(
            stream_name=stream_name,
            app_name=app_name,
            shard_index=shard_index,
            seconds_per_checkpoint=seconds_per_checkpoint,
            consumer_mode=consumer_mode,
            endpoint_url=endpoint_url,
            checkpointer=checkpointer,
            output_mode=output_mode,
            output_stream_names_filter=output_stream_names_filter,
            input_stream_index=input_stream_index,
            enable_backup_input_stream=enable_backup_input_stream,
            include_sequence_number=include_sequence_number,
        )

        self._record_pack_size: int = 0
        self._next_put_tstamp: float = time.time()
        self._record_pack: dict[str, list[Any]] = defaultdict(list)
        self._auto_flush_num_records = auto_flush_num_records

    async def get_shard_id(self) -> str:
        shard = await self._transport_layer.get_shard()
        return shard["ShardId"]

    def _get_consumer_key(self) -> str:
        return f"{self.app_name}/{self.name}/{self.shard_index}"

    async def _flush_records(self) -> None:
        # Chatty version useful for debugging
        # counts = {k: len(v) for k, v in self._record_pack.items()}
        # logging.info(f"Dumping packed records: {self._record_pack_size} {counts}")

        await self._transport_layer.put_records(self._record_pack)

        # Ensure that there's no "flush spew" if there's a slowdown
        if self._auto_flush_s:
            while self._next_put_tstamp <= time.time():
                self._next_put_tstamp += self._auto_flush_s

        self._record_pack = defaultdict(list)
        self._record_pack_size = 0

    async def put(self, record: dict[str, Any], key: str = "default") -> bool:
        async with self._put_lock:
            self._put(record, key)

            if self._should_auto_flush:
                await self._flush_records()
                return True
            else:
                return False

    async def put_batch(
        self,
        batch: Iterable[tuple[dict[str, Any], str]],
    ) -> None:
        async with self._put_lock:
            for record, key in batch:
                self._put(record, key)

                if self._should_auto_flush:
                    await self._flush_records()

    def _put(self, record: dict[str, Any], key: str = "default") -> None:
        self._record_pack[key].append(record)
        self._record_pack_size += 1

    @property
    def _should_auto_flush(self) -> bool:
        return (
            self._auto_flush_num_records is not None
            and self._record_pack_size >= self._auto_flush_num_records
        ) or (bool(self._auto_flush_s) and time.time() >= self._next_put_tstamp)

    async def flush(self) -> bool:
        async with self._put_lock:
            if self._record_pack_size:
                await self._flush_records()
                return True
            else:
                log_bsdebug("%s.flush() called but there's no data to flush", self.name)
                return False

    async def list_streams_worker(self) -> None:
        if not self._list_streams_interval_secs:
            return

        refresh_interval = self._list_streams_interval_secs
        next_refresh = int((time.time() // refresh_interval) * refresh_interval)
        logging.info(
            f"Starting list stream worker, running every {self._list_streams_interval_secs}s"
        )
        while True:
            try:
                next_refresh += refresh_interval
                wait_time = next_refresh - time.time()

                if wait_time > 0:
                    await asyncio.sleep(wait_time)

                logging.info("listing output stream names...")

                if not await self._transport_layer.update_output_stream_list():
                    logging.warning("failed to list output stream names...")

            except Exception as e:
                logging.exception(f"Unable to update output streams names: {e}")

    def __repr__(self) -> str:
        return f"<Channel {self._get_consumer_key()} {self._endpoint=:}>"

    def __aiter__(self) -> AsyncIterator[Any]:
        return self._transport_layer.iterate_records(
            start_timestamp=self._start_timestamp,
            start_sequence_number=self._start_sequence_number,
        )
