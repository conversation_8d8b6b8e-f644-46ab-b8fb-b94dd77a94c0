import asyncio
import json
from unittest.mock import AsyncMock, patch

import pytest

from block_stream.consumer.auto_scaling_cache_manager import AutoScalingCacheManager
from block_stream.consumer.cache import CalculationCache


class MockCalculationCache(CalculationCache):
    """Mock cache for testing that satisfies the CacheT type constraint"""

    def __init__(self, name: str = "mock_cache"):
        # Initialize with minimal required parameters for CalculationCache
        super().__init__(caches=[], name=name)
        self.mock_name = name

    def __eq__(self, other: object) -> bool:
        if isinstance(other, MockCalculationCache):
            return self.mock_name == other.mock_name
        return False

    def __hash__(self) -> int:
        return hash(self.mock_name)


def test_initialisation() -> None:
    async def mock_create_cache(target: int) -> MockCalculationCache:
        return MockCalculationCache(f"cache_{target}")

    manager = AutoScalingCacheManager([1, 2, 3], mock_create_cache)
    assert manager._target_jobs == [1, 2, 3]


@pytest.fixture
def sample_data_sets() -> list[dict[str, object]]:
    return [
        {"id": 1, "data": {}, "output_frequency": 2 * 1e9},
        {"id": 2, "data": {}, "output_frequency": 10 * 1e9},
        {"id": 2, "data": {}, "output_frequency": 5 * 1e9},
    ]


@pytest.fixture
def default_ns() -> int:
    return 1  # Something very different to seconds


@pytest.mark.asyncio
async def test_rebalance_caches_single_instance(
    sample_data_sets: list[dict[str, object]], default_ns: int
) -> None:
    async def mock_create_cache(target: str) -> MockCalculationCache:
        return MockCalculationCache(f"cache_for_{target}")

    stringified_ds = [json.dumps(ds) for ds in sample_data_sets]
    manager = AutoScalingCacheManager(
        stringified_ds,
        mock_create_cache,
    )

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        await manager.rebalance_caches()

    assert set(manager._caches.keys()) == set(stringified_ds)
    assert manager.get_maximum_ns(default_ns) == sample_data_sets[1]["output_frequency"]


def test_rebalance_caches_rebalance(
    sample_data_sets: list[dict[str, object]], default_ns: int
) -> None:
    stringified_ds = [json.dumps(ds) for ds in sample_data_sets]

    async def mock_create_cache(target: str) -> MockCalculationCache:
        return MockCalculationCache(f"cache_for_{target}")

    manager = AutoScalingCacheManager(
        stringified_ds,
        mock_create_cache,
    )
    assert manager.get_maximum_ns(default_ns) == 1

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 2,
            "current_task_index": 0,
        }

        asyncio.run(manager.rebalance_caches())

    assert set(manager._caches.keys()) == {stringified_ds[0], stringified_ds[2]}
    assert manager.get_maximum_ns(default_ns) == sample_data_sets[2]["output_frequency"]

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 2,
            "current_task_index": 1,
        }

        asyncio.run(manager.rebalance_caches())

    assert list(manager._caches.keys()) == [stringified_ds[1]]
    assert manager.get_maximum_ns(default_ns) == sample_data_sets[1]["output_frequency"]


def test_async_cache_creation_with_failures() -> None:
    """Test that async cache creation handles failures gracefully"""

    async def failing_cache_creation(target: str) -> MockCalculationCache:
        if target == "failing_job":
            raise ValueError(f"Cache creation failed for {target}")
        return MockCalculationCache(f"cache_for_{target}")

    target_jobs = ["job1", "failing_job", "job2"]
    manager = AutoScalingCacheManager(target_jobs, failing_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        # Should not raise an exception even with failing cache creation
        asyncio.run(manager.rebalance_caches())

    assert set(manager._caches.keys()) == {"job1", "job2"}
    assert manager._caches["job1"].mock_name == "cache_for_job1"
    assert manager._caches["job2"].mock_name == "cache_for_job2"
    assert "failing_job" not in manager._caches


def test_async_cache_creation_all_failures() -> None:
    """Test that async cache creation handles the case where all creations fail"""

    async def always_failing_cache_creation(target: str) -> MockCalculationCache:
        raise ValueError(f"Cache creation failed for {target}")

    target_jobs = ["a", "b"]
    manager = AutoScalingCacheManager(target_jobs, always_failing_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        # Should not raise an exception even when all cache creations fail
        asyncio.run(manager.rebalance_caches())

    assert len(manager._caches) == 0


@pytest.mark.asyncio
async def test_sync_cache_creation() -> None:
    """Test that synchronous cache creation functions work correctly"""

    def sync_create_cache(target: str) -> MockCalculationCache:
        return MockCalculationCache(f"sync_cache_for_{target}")

    target_jobs = ["job1", "job2", "job3"]
    manager = AutoScalingCacheManager(target_jobs, sync_create_cache)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        await manager.rebalance_caches()

    # Verify all caches were created
    assert len(manager._caches) == 3
    assert set(manager._caches.keys()) == {"job1", "job2", "job3"}

    # Verify cache content and types
    assert manager._caches["job1"].mock_name == "sync_cache_for_job1"
    assert manager._caches["job2"].mock_name == "sync_cache_for_job2"
    assert manager._caches["job3"].mock_name == "sync_cache_for_job3"

    # Verify all are instances of the expected type
    for cache in manager._caches.values():
        assert isinstance(cache, MockCalculationCache)

    # Test that the manager iterator works
    cache_list = list(manager)
    assert len(cache_list) == 3
    assert all(isinstance(cache, MockCalculationCache) for cache in cache_list)


@pytest.mark.asyncio
async def test_mixed_sync_async_cache_creation() -> None:
    """Test that the manager can handle both sync and async cache creation in the same run"""

    from collections.abc import Awaitable

    # Use target-based deterministic behavior instead of call count
    def mixed_cache_creation(
        target: str,
    ) -> MockCalculationCache | Awaitable[MockCalculationCache]:
        # Use target name to determine sync vs async for deterministic behavior
        if target in {"job1", "job3"}:
            # Return async cache for specific targets
            async def async_cache() -> MockCalculationCache:
                return MockCalculationCache(f"async_cache_for_{target}")

            return async_cache()
        else:
            # Return sync cache for other targets
            return MockCalculationCache(f"sync_cache_for_{target}")

    # Test with different target jobs to trigger multiple cache creations
    target_jobs = ["job1", "job2", "job3", "job4"]
    manager = AutoScalingCacheManager(target_jobs, mixed_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        await manager.rebalance_caches()

    # Verify all caches were created
    assert len(manager._caches) == 4
    assert set(manager._caches.keys()) == {"job1", "job2", "job3", "job4"}

    # Verify the correct cache types were created
    assert manager._caches["job1"].mock_name == "async_cache_for_job1"
    assert manager._caches["job2"].mock_name == "sync_cache_for_job2"
    assert manager._caches["job3"].mock_name == "async_cache_for_job3"
    assert manager._caches["job4"].mock_name == "sync_cache_for_job4"

    # Verify all caches are of the same type (MockCalculationCache)
    for cache in manager._caches.values():
        assert isinstance(cache, MockCalculationCache)


@pytest.mark.asyncio
async def test_mixed_sync_async_cache_creation_with_failures() -> None:
    """Test error handling when both sync and async cache creation can fail"""

    from collections.abc import Awaitable

    def failing_mixed_cache_creation(
        target: str,
    ) -> MockCalculationCache | Awaitable[MockCalculationCache]:
        if target == "sync_fail":
            raise ValueError(f"Sync cache creation failed for {target}")
        elif target == "async_fail":

            async def failing_async_cache() -> MockCalculationCache:
                raise RuntimeError(f"Async cache creation failed for {target}")

            return failing_async_cache()
        elif target == "async_success":

            async def async_cache() -> MockCalculationCache:
                return MockCalculationCache(f"async_cache_for_{target}")

            return async_cache()
        else:
            return MockCalculationCache(f"sync_cache_for_{target}")

    target_jobs = ["sync_success", "sync_fail", "async_success", "async_fail"]
    manager = AutoScalingCacheManager(target_jobs, failing_mixed_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        # Should not raise an exception even with mixed failures
        await manager.rebalance_caches()

    # Only successful caches should be created
    assert len(manager._caches) == 2
    assert set(manager._caches.keys()) == {"sync_success", "async_success"}
    assert manager._caches["sync_success"].mock_name == "sync_cache_for_sync_success"
    assert manager._caches["async_success"].mock_name == "async_cache_for_async_success"


@pytest.mark.asyncio
async def test_sync_cache_creation_with_exception() -> None:
    """Test that sync cache creation exceptions are handled gracefully"""

    def failing_sync_cache_creation(target: str) -> MockCalculationCache:
        if target == "failing_target":
            raise ValueError(f"Sync cache creation failed for {target}")
        return MockCalculationCache(f"cache_for_{target}")

    target_jobs = ["good_target", "failing_target", "another_good_target"]
    manager = AutoScalingCacheManager(target_jobs, failing_sync_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        # Should not raise an exception even with sync failures
        await manager.rebalance_caches()

    # Only successful caches should be created
    assert len(manager._caches) == 2
    assert set(manager._caches.keys()) == {"good_target", "another_good_target"}
    assert manager._caches["good_target"].mock_name == "cache_for_good_target"
    assert (
        manager._caches["another_good_target"].mock_name
        == "cache_for_another_good_target"
    )


@pytest.mark.asyncio
async def test_create_cache_method_directly() -> None:
    """Test the _create_cache method directly for both sync and async cases"""

    def sync_cache_fn(target: str) -> MockCalculationCache:
        return MockCalculationCache(f"direct_sync_{target}")

    async def async_cache_fn(target: str) -> MockCalculationCache:
        return MockCalculationCache(f"direct_async_{target}")

    # Test sync cache creation
    sync_manager = AutoScalingCacheManager(["test"], sync_cache_fn)
    sync_result = await sync_manager._create_cache("test_target")
    assert isinstance(sync_result, MockCalculationCache)
    assert sync_result.mock_name == "direct_sync_test_target"

    # Test async cache creation
    async_manager = AutoScalingCacheManager(["test"], async_cache_fn)
    async_result = await async_manager._create_cache("test_target")
    assert isinstance(async_result, MockCalculationCache)
    assert async_result.mock_name == "direct_async_test_target"


@pytest.mark.asyncio
async def test_create_cache_method_with_task() -> None:
    """Test the _create_cache method when a Task is returned (should be treated as sync)"""

    def task_cache_fn(target: str) -> asyncio.Task[MockCalculationCache]:
        """
        Cache function that returns a Task.
        Tasks are awaitable but should be treated as sync since they're already running.
        """

        async def create_mock_cache() -> MockCalculationCache:
            return MockCalculationCache(f"task_cache_{target}")

        # Create and return a Task (this simulates a cache function that returns an already-running Task)
        return asyncio.create_task(create_mock_cache())

    # Test Task cache creation
    task_manager = AutoScalingCacheManager(["test"], task_cache_fn)
    task_result = await task_manager._create_cache("test_target")

    # The result should be the Task itself (not awaited), but since it's a running Task,
    # we should be able to await it to get the actual cache
    assert isinstance(task_result, asyncio.Task)

    # When we await the Task, we should get our MockCalculationCache
    actual_cache = await task_result
    assert isinstance(actual_cache, MockCalculationCache)
    assert actual_cache.mock_name == "task_cache_test_target"


def test_async_cache_creation_with_mixed_types() -> None:
    """Test that async cache creation maintains type consistency checks"""
    call_count = 0

    # Create a different type of cache that will fail the isinstance check
    class DifferentMockCache(CalculationCache):
        def __init__(self) -> None:
            super().__init__(caches=[], name="different")

    async def mixed_type_cache_creation(target: str) -> CalculationCache:
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            return MockCalculationCache("string_cache")
        else:
            return DifferentMockCache()  # This will cause the assertion error

    target_jobs = ["job1", "job2"]
    manager = AutoScalingCacheManager(target_jobs, mixed_type_cache_creation)

    with patch.object(
        manager._ecs_manager, "get_task_info", new_callable=AsyncMock
    ) as mock_get_task_info:
        mock_get_task_info.return_value = {
            "running_task_count": 1,
            "current_task_index": 0,
        }

        with pytest.raises(AssertionError):
            asyncio.run(manager.rebalance_caches())
