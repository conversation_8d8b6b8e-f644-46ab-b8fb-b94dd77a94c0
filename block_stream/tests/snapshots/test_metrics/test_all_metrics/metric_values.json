[{"MetricName": "multi_lag_metric", "Dimensions": [{"Name": "field1", "Value": "value1"}, {"Name": "field2", "Value": "value2"}, {"Name": "Component", "Value": "test"}], "StatisticValues": {"SampleCount": 1, "Sum": 1.0, "Minimum": 1.0, "Maximum": 1.0}}, {"MetricName": "multi_rate_metric", "Dimensions": [{"Name": "field1", "Value": "value1"}, {"Name": "field2", "Value": "value2"}, {"Name": "Component", "Value": "test"}], "Value": 1.0}, {"MetricName": "multi_num_metric", "Dimensions": [{"Name": "field1", "Value": "value1"}, {"Name": "field2", "Value": "value2"}, {"Name": "Component", "Value": "test"}], "StatisticValues": {"SampleCount": 1, "Sum": 1.0, "Minimum": 1, "Maximum": 1}}, {"MetricName": "multi_callable_metric", "Dimensions": [{"Name": "field1", "Value": "value1"}, {"Name": "field2", "Value": "value2"}, {"Name": "Component", "Value": "test"}], "Value": 1.0}, {"MetricName": "rate_metric", "Dimensions": [{"Name": "Component", "Value": "test"}], "Value": 1.0}, {"MetricName": "callable_metric", "Dimensions": [{"Name": "Component", "Value": "test"}], "Value": 1.0}]