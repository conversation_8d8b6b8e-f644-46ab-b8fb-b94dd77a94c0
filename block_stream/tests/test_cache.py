import copy
from datetime import UTC, datetime, timedelta
from typing import Any

import pytest
from utils_general import from_iso

from block_stream.consumer.cache import (
    <PERSON><PERSON>,
    CalculationCache,
    DerivedDataCache,
    HistoricalTickCache,
    LastTickCache,
    TickCalcCache,
    _allow_catalog_update,
    _convert_to_instrument_details,
    _filter_data,
    _is_tick_preserved,
)
from block_stream.consumer.typings import (
    CalculatorCacheInput,
    CalculatorCacheOutput,
    QualifiedNameToLastTick,
    TimeStampToTickData,
)
from block_stream.typings import (
    CatalogData,
    CatalogFilter,
    InstrumentDetails,
    InstrumentsDetailsMap,
)


@pytest.fixture
def expired_instrument() -> InstrumentDetails:
    return {
        "qualified_name": "bybit.option.contracts",
        "instrument_name": "BTC-28SEP22-20000-C",
        "instrument": "BTC-28SEP22-20000-C",
        "baseAsset": "BTC",
        "quoteAsset": "USDC",
        "availableSince": "2022-09-26T08:00:00.000Z",
        "listing": "2022-09-26T08:00:00.000Z",
        "expiry": (datetime.now(UTC) - timedelta(days=1)).isoformat(),
        "type": "C",
        "strike": 20000.0,
    }


@pytest.fixture
def valid_instrument() -> InstrumentDetails:
    return {
        "qualified_name": "bybit.option.contracts",
        "instrument_name": "BTC-28SEP99-20000-C",
        "instrument": "BTC-28SEP99-20000-C",
        "baseAsset": "BTC",
        "quoteAsset": "USDC",
        "availableSince": "2022-09-26T08:00:00.000Z",
        "listing": "2022-09-26T08:00:00.000Z",
        "expiry": (datetime.now(UTC) + timedelta(days=1)).isoformat(),
        "type": "C",
        "strike": 20000.0,
    }


@pytest.fixture
def sample_ticks(
    expired_instrument: InstrumentDetails,
    valid_instrument: InstrumentDetails,
) -> list[Any]:
    return [
        {
            "qualified_name": f"bybit.option.{expired_instrument['instrument_name']}.tick.px",
            "timestamp": int((datetime.now(UTC) - timedelta(days=1)).timestamp() * 1e9),
            "px": 120.0,
        },
        {
            "qualified_name": f"bybit.option.{valid_instrument['instrument_name']}.tick.px",
            "timestamp": int((datetime.now(UTC) + timedelta(days=1)).timestamp() * 1e9),
            "px": 130.0,
        },
    ]


@pytest.fixture
def sample_last_ticks(sample_ticks: list[Any]) -> QualifiedNameToLastTick:
    return {tick["qualified_name"]: tick for tick in sample_ticks}


class StubTickCache(Cache):
    def __init__(self, ticks: list[Any]):
        self._ticks = ticks

    def get_data(self, deepcopy: bool = True) -> dict[str, Any]:
        return {"raw_data": self._ticks}

    def accepts(self, data: Any) -> bool:
        return False

    def on_clean_up(self) -> None:
        pass

    def on_new_data(self, data: Any) -> bool:
        return False


class TestCalculationCache:
    def test_get_data_merges_lists(self, sample_ticks: list[Any]) -> None:
        tick = {
            "qualified_name": "exchange.asset_class.instrument.tick.px",
            "px": 1.0,
        }
        cache1 = StubTickCache(sample_ticks)
        cache2 = StubTickCache([tick])
        calc_cache = CalculationCache([cache1, cache2], name="")

        cached_ticks: list[Any] = calc_cache.get_data()["raw_data"]

        assert all(tick in cached_ticks for tick in sample_ticks)
        assert tick in cached_ticks

    def test_get_data_dynamic_params(self, sample_ticks: list[Any]) -> None:
        def generate_dynamic_params() -> dict[str, str]:
            return {"dynamic": "param"}

        cache = StubTickCache([])
        calc_cache = CalculationCache(
            [cache], name="", generate_dynamic_params=generate_dynamic_params
        )
        cached_data = calc_cache.get_data()

        assert "dynamic" in cached_data


class TestLastTickCache:
    def test_on_clean_up_remove_old_instruments(
        self,
        expired_instrument: InstrumentDetails,
        valid_instrument: InstrumentDetails,
        sample_last_ticks: QualifiedNameToLastTick,
    ) -> None:
        instruments: InstrumentsDetailsMap = {
            f"bybit.option.{expired_instrument['instrument_name']}": expired_instrument,
            f"bybit.option.{valid_instrument['instrument_name']}": valid_instrument,
        }

        cache = LastTickCache(
            prefetched_ticks=sample_last_ticks, prefetched_instruments=instruments
        )
        cache.on_clean_up()

        data = cache.get_data()
        assert "instruments" in data
        cached_instruments = data["instruments"]
        cached_qns = [x["qualified_name"] for x in data["raw_data"]]
        assert (
            f"bybit.option.{expired_instrument['instrument_name']}"
            not in cached_instruments
        )
        assert (
            f"bybit.option.{expired_instrument['instrument_name']}.tick.px"
            not in cached_qns
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}" in cached_instruments
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}.tick.px" in cached_qns
        )


@pytest.fixture
def old_tick() -> dict[str, object]:
    return {
        "qualified_name": "exchange.asset_type.instrument.tick.px",
        "timestamp": int((datetime.now(UTC) - timedelta(days=1)).timestamp() * 1e9),
        "px": 100.0,
    }


@pytest.fixture
def recent_tick() -> dict[str, object]:
    return {
        "qualified_name": "exchange.asset_type.instrument.tick.px",
        "timestamp": int(datetime.now(UTC).timestamp() * 1e9),
        "px": 110.0,
    }


class TestHistoricalTickCache:
    def test_clean_up_deletes_old_ticks(
        self, old_tick: dict[str, object], recent_tick: dict[str, object]
    ) -> None:
        cache = HistoricalTickCache(
            prefetched_ticks=[old_tick, recent_tick],
            prefetched_instruments={},
            time_window=timedelta(hours=12),
        )
        cache.on_clean_up()
        cached_ticks = cache.get_data()["raw_data"]
        assert len(cached_ticks) == 1
        assert cached_ticks[0] == recent_tick

    def test_add_catalog_entry_to_cache_with_filter_invalid_instrument(
        self, valid_instrument: InstrumentDetails
    ) -> None:
        cache = HistoricalTickCache(
            prefetched_ticks=[],
            prefetched_instruments={},
            time_window=timedelta(hours=12),
            catalog_filters=[
                {
                    "asset_class": ["option"],
                    "base_assets": ["BTC"],
                    "exchanges": ["deribit"],
                    "quote_assets": ["BTC"],
                    "suffixes": ["mid.px"],
                }
            ],
        )
        dpm_instrument = copy.copy(valid_instrument)
        dpm_instrument["q"] = dpm_instrument["qualified_name"]  # type: ignore
        del dpm_instrument["qualified_name"]

        cache.on_new_data(dpm_instrument)
        assert cache.get_data()["instruments"] == {}

    def test_add_catalog_entry_to_cache_with_filter_valid_instrument(self) -> None:
        cache = HistoricalTickCache(
            prefetched_ticks=[],
            prefetched_instruments={},
            time_window=timedelta(hours=12),
            catalog_filters=[
                {
                    "asset_class": ["option"],
                    "base_assets": ["BTC"],
                    "exchanges": ["deribit"],
                    "quote_assets": ["BTC"],
                    "suffixes": ["volume.amt"],
                }
            ],
        )

        opt_instrument: CatalogData = {
            "q": "deribit.option.contracts",
            "baseAsset": "BTC",
            "quoteAsset": "BTC",
            "instrument": "BTC-01JAN24-40000-C",
            "expiry": (datetime.now(UTC) - timedelta(days=1)).isoformat(),
            "availableSince": "2023-01-01",
            "listing": "test",
            "type": "option",
            "strike": 4000.0,
        }
        pre_mutation = copy.copy(opt_instrument)

        cache.on_new_data(opt_instrument)
        instruments: InstrumentsDetailsMap = cache.get_data().get("instruments", {})

        converted_instrument = _convert_to_instrument_details(opt_instrument, False)

        assert instruments == {
            f"deribit.option.{converted_instrument['instrument_name']}": converted_instrument
        }
        assert (
            instruments[f"deribit.option.{converted_instrument['instrument_name']}"][
                "instrument_name"
            ]
            == pre_mutation["instrument"]
        )
        assert (
            instruments[
                f"deribit.option.{converted_instrument['instrument_name']}"
            ].get("qualified_name")
            == pre_mutation["q"]
        )

    def test_on_clean_up_remove_old_instruments(
        self,
        expired_instrument: InstrumentDetails,
        valid_instrument: InstrumentDetails,
        sample_ticks: list[Any],
    ) -> None:
        instruments: InstrumentsDetailsMap = {
            f"bybit.option.{expired_instrument['instrument_name']}": expired_instrument,
            f"bybit.option.{valid_instrument['instrument_name']}": valid_instrument,
        }

        cache = HistoricalTickCache(
            time_window=timedelta(hours=12),
            prefetched_ticks=sample_ticks,
            prefetched_instruments=instruments,
        )
        data = cache.get_data()
        assert "instruments" in data
        cached_instruments = data["instruments"]
        assert len(cached_instruments) == 2
        cache.on_clean_up()

        data = cache.get_data()
        assert "instruments" in data
        cached_instruments = data["instruments"]
        assert len(cached_instruments) == 1

        cached_qns = [x["qualified_name"] for x in data["raw_data"]]

        assert (
            f"bybit.option.{expired_instrument['instrument_name']}"
            not in cached_instruments
        )
        assert (
            f"bybit.option.{expired_instrument['instrument_name']}.tick.px"
            not in cached_qns
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}" in cached_instruments
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}.tick.px" in cached_qns
        )


@pytest.fixture
def stream_tick() -> Any:
    return {
        "q": "exchange.asset_type.instrument.tick.px",
        "t": int(datetime.now(UTC).timestamp() * 1e9),
        "v": 110.0,
    }


def stream_tick_with_px(prices: list[float]) -> list[Any]:
    return [
        {
            "q": "exchange.asset_type.instrument.tick.px",
            "t": int(datetime.now(UTC).timestamp() * 1e9),
            "p": px,
        }
        for px in prices
    ]


class TestTickCalcCache:
    def test_data_when_copy_enabled(self, stream_tick: Any) -> None:
        cache = TickCalcCache(
            tick_calc_fn=lambda *_: {},
            copy_new_data=True,
        )

        original_tick = copy.copy(stream_tick)
        cache.on_new_data(stream_tick)
        assert stream_tick == original_tick

    def test_data_when_copy_disabled(self, stream_tick: Any) -> None:
        cache = TickCalcCache(
            tick_calc_fn=lambda *_: {},
            copy_new_data=False,
        )
        original_tick = copy.deepcopy(stream_tick)
        cache.on_new_data(stream_tick)
        assert stream_tick != original_tick

    def test_get_data_when_return_instrument_disabled(self) -> None:
        cache = TickCalcCache(tick_calc_fn=lambda *_: {}, return_instruments=False)
        data = cache.get_data()
        assert "instruments" not in data
        assert "calculated_data" in data

    def test_on_clean_up_remove_old_instruments_and_calc_data(
        self,
        expired_instrument: InstrumentDetails,
        valid_instrument: InstrumentDetails,
        sample_ticks: list[Any],
    ) -> None:
        instruments: InstrumentsDetailsMap = {
            f"bybit.option.{expired_instrument['instrument_name']}": expired_instrument,
            f"bybit.option.{valid_instrument['instrument_name']}": valid_instrument,
        }

        prefetched_calc_input: CalculatorCacheInput = {
            f"bybit.option.{expired_instrument['instrument_name']}": sample_ticks[0],
            f"bybit.option.{valid_instrument['instrument_name']}": sample_ticks[1],
        }

        prefetched_calc_output: CalculatorCacheOutput = {
            f"bybit.option.{expired_instrument['instrument_name']}": sample_ticks[0],
            f"bybit.option.{valid_instrument['instrument_name']}": sample_ticks[1],
        }

        def clean_up_fn(
            calc_input: CalculatorCacheInput,
            calc_output: CalculatorCacheOutput,
            expired_instruments: set[str],
        ) -> tuple[CalculatorCacheInput, CalculatorCacheOutput]:
            calc_input = {
                k: v
                for k, v in calc_input.items()
                if _is_tick_preserved(v, expired_instruments)
            }
            calc_output = {
                k: v
                for k, v in calc_output.items()
                if _is_tick_preserved(v, expired_instruments)
            }
            return calc_input, calc_output

        cache = TickCalcCache(
            tick_calc_fn=lambda **_: {},
            prefetched_instruments=instruments,
            preloaded_calc_input=prefetched_calc_input,
            preloaded_calc_output=prefetched_calc_output,
            cleanup_fn=clean_up_fn,
        )

        cache.on_clean_up()

        data = cache.get_data()

        assert (
            f"bybit.option.{expired_instrument['instrument_name']}"
            not in data["instruments"]
        )
        assert (
            f"bybit.option.{expired_instrument['instrument_name']}"
            not in data["calculated_data"]
        )
        assert (
            f"bybit.option.{expired_instrument['instrument_name']}"
            not in cache._calc_input
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}" in data["instruments"]
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}"
            in data["calculated_data"]
        )
        assert (
            f"bybit.option.{valid_instrument['instrument_name']}" in cache._calc_input
        )

    def test_calc_fn_results(self) -> None:
        def average_px(
            last_tick: dict[str, Any],
            prev_data: dict[str, Any],
            calc_output: dict[str, Any],
            _: InstrumentsDetailsMap,
        ) -> dict[str, Any]:
            if not last_tick["px"]:
                return {}

            if not calc_output:
                calc_output["count"] = 0
                calc_output["average"] = 0.0

            if not prev_data:
                prev_data["prices"] = []

            prev_data["prices"].append(last_tick["px"])
            calc_output["count"] += 1
            calc_output["average"] = sum(prev_data["prices"]) / calc_output["count"]

            return calc_output

        cache = TickCalcCache(
            tick_calc_fn=average_px,
        )

        prices = [10, 10.5, 10.3, 10.1]
        for tick in stream_tick_with_px(prices):
            cache.on_new_data(tick)

        output = cache.get_data()
        assert output["calculated_data"]["average"] == sum(prices) / len(prices)


@pytest.fixture
def valid_datapoint() -> dict[str, object]:
    valid_date = datetime.now(UTC) + timedelta(days=1)
    return {
        "qualified_name": f"v2lyra-supercomposite.option.BTC.SVI.{valid_date.strftime('%Y-%m-%dT%H:%M:%SZ')}.live.params",
        "timestamp": int(valid_date.timestamp() * 1e9),
        "v": {"forward": 40000},
    }


@pytest.fixture
def expired_datapoint() -> dict[str, object]:
    expired_date = datetime.now(UTC) - timedelta(days=1)
    return {
        "qualified_name": f"v2lyra-supercomposite.option.ETH.SVI.{expired_date.strftime('%Y-%m-%dT%H:%M:%SZ')}.live.params",
        "timestamp": int(expired_date.timestamp() * 1e9),
        "v": {"forward": 40000},
    }


class TestDerivedDataCache:
    def test_on_clean_up_old_instruments(
        self, valid_datapoint: dict[str, Any], expired_datapoint: dict[str, Any]
    ) -> None:
        datapoints: dict[str, Any] = {
            valid_datapoint["qualified_name"]: valid_datapoint,
            expired_datapoint["qualified_name"]: expired_datapoint,
        }

        def expired_qn(
            dps: QualifiedNameToLastTick | TimeStampToTickData,
        ) -> QualifiedNameToLastTick:
            return {
                str(k): v
                for k, v in dps.items()
                if from_iso(v["qualified_name"].split(".")[4]) > datetime.now(tz=UTC)
            }

        cache = DerivedDataCache(
            cleanup_fn=expired_qn,
            prefetched_datapoints=datapoints,
        )

        cache.on_clean_up()

        data = cache.get_data()
        assert "raw_data" in data
        assert len(data["raw_data"]) == 1
        assert (
            data["raw_data"][0]["qualified_name"] == valid_datapoint["qualified_name"]
        )


def test_filter_data_valid(valid_instrument: Any) -> None:
    filters: list[CatalogFilter] = [
        {
            "exchanges": ["bybit", "binance"],
            "asset_class": ["future"],
            "base_assets": ["BTC"],
            "suffixes": ["tick.bid.px", "tick.ask.px"],
        }
    ]
    instruments: InstrumentsDetailsMap = {
        "bybit.future.BTCUSDH24": valid_instrument,
        "binance.future.BTCUSD_240329": valid_instrument,
    }

    data = {
        "q": "bybit.future.BTCUSDH24.tick.bid.px",
    }
    assert _filter_data(data=data, filters=filters, instruments=instruments)

    data = {
        "q": "binance.future.BTCUSD_240329.tick.ask.px",
    }
    assert _filter_data(data=data, filters=filters, instruments=instruments)


def test_filter_data_invalid(valid_instrument: Any) -> None:
    filters: list[CatalogFilter] = [
        {
            "exchanges": ["binance", "bybit"],
            "asset_class": ["future"],
            "base_assets": ["BTC"],
            "suffixes": ["tick.bid.px", "tick.ask.px"],
        }
    ]
    instruments: InstrumentsDetailsMap = {
        "bybit.future.BTCUSDH24": valid_instrument,
        "binance.future.BTCUSD_240329": valid_instrument,
    }

    # Invalid suffix - price
    data = {
        "q": "bybit.future.BTCUSDH24.tick.mid.px",
    }
    assert not _filter_data(data=data, filters=filters, instruments=instruments)

    # Invalid suffix - frequency
    data = {
        "q": "bybit.future.BTCUSDH24.live.bid.px",
    }
    assert not _filter_data(data=data, filters=filters, instruments=instruments)

    # Invalid instrument
    data = {
        "q": "binance.future.BTCUSD_240327.tick.ask.px",
    }
    assert not _filter_data(data=data, filters=filters, instruments=instruments)


def test_allow_catalog_update_valid() -> None:
    filters: list[CatalogFilter] = [
        {
            "exchanges": ["bybit"],
            "asset_class": ["spot"],
            "base_assets": ["BTC"],
            "quote_assets": ["USD"],
            "suffixes": [],
        }
    ]

    data: CatalogData = {
        "q": "bybit.spot.contract",
        "instrument": "BTCUSD",
        "baseAsset": "BTC",
        "quoteAsset": "USD",
    }
    assert _allow_catalog_update(catalog_data=data, catalog_filters=filters)


def test_allow_catalog_update_invalid() -> None:
    filters: list[CatalogFilter] = [
        {
            "exchanges": ["bybit"],
            "asset_class": ["spot"],
            "base_assets": ["BTC"],
            "quote_assets": ["USD"],
            "suffixes": [],
        }
    ]

    # Invalid Exchange
    data: CatalogData = {
        "q": "binance.spot.contract",
        "instrument": "BTCUSD",
        "baseAsset": "BTC",
        "quoteAsset": "USD",
    }
    assert not _allow_catalog_update(catalog_data=data, catalog_filters=filters)

    # Invalid asset_class
    data = {
        "q": "bybit.future.contract",
        "instrument": "BTCUSD",
        "baseAsset": "BTC",
        "quoteAsset": "USD",
    }
    assert not _allow_catalog_update(catalog_data=data, catalog_filters=filters)

    # Invalid base
    data = {
        "q": "bybit.spot.contract",
        "instrument": "ETHUSD",
        "baseAsset": "ETH",
        "quoteAsset": "USD",
    }
    assert not _allow_catalog_update(catalog_data=data, catalog_filters=filters)

    # Invalid quote
    data = {
        "q": "bybit.spot.contract",
        "instrument": "BTCUSDT",
        "baseAsset": "BTC",
        "quoteAsset": "USDT",
    }
    assert not _allow_catalog_update(catalog_data=data, catalog_filters=filters)


def test_filter_data_multiple_filters(valid_instrument: Any) -> None:
    # Testing multiple filters with different suffixes per asset class
    filters: list[CatalogFilter] = [
        {
            "exchanges": ["bybit"],
            "asset_class": ["future"],
            "base_assets": ["BTC"],
            "suffixes": ["mid.px", "tick.px"],
        },
        {
            "exchanges": ["bybit"],
            "asset_class": ["option"],
            "base_assets": ["BTC"],
            "suffixes": ["bid.px", "ask.px"],
        },
    ]
    instruments: InstrumentsDetailsMap = {
        "bybit.future.BTCUSDH24": valid_instrument,
        "bybit.option.BTC-28SEP99-20000-C": valid_instrument,
    }

    # Invalid suffix for option - price
    data = {
        "q": "bybit.option.BTC-28SEP99-20000-C.tick.mid.px",
    }
    assert not _filter_data(data=data, filters=filters, instruments=instruments)

    # Invalid suffix for future - price
    data = {
        "q": "bybit.future.BTCUSDH24.tick.bid.px",
    }
    assert not _filter_data(data=data, filters=filters, instruments=instruments)

    # Valid suffix for option - price
    data = {
        "q": "bybit.option.BTC-28SEP99-20000-C.tick.bid.px",
    }
    assert _filter_data(data=data, filters=filters, instruments=instruments)

    # Valid suffix for future - price
    data = {
        "q": "bybit.future.BTCUSDH24.tick.mid.px",
    }
    assert _filter_data(data=data, filters=filters, instruments=instruments)
