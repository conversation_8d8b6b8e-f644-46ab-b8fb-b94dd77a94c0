import asyncio
import time
from unittest.mock import AsyncMock

from block_stream.utils.ecs_cluster_manager import EcsClusterManager

TASK_ID = "08ff47d779d547f9a78f23671bb300dd"
VALID_ARN = f"arn:aws:ecs:eu-west-2:685767522279:task/Production/{TASK_ID}"


def setup_ecm(task_info_cache_sec: int = 0) -> EcsClusterManager:
    cluster_manager = EcsClusterManager(task_info_cache_sec=task_info_cache_sec)
    cluster_manager._task_arn = VALID_ARN
    cluster_manager._family_name = "Task"
    cluster_manager._get_running_ecs_tasks = AsyncMock(  # type: ignore
        return_value=[
            {"taskArn": "123"},
            {"taskArn": VALID_ARN},
        ]
    )
    cluster_manager._get_service_info = AsyncMock(  # type: ignore
        return_value={
            "desiredCount": 2,
            "pendingCount": 0,
            "runningCount": 2,
        }
    )
    return cluster_manager


async def async_test(cluster_manager: EcsClusterManager) -> None:
    task_info = await cluster_manager.get_task_info()
    assert task_info == {
        "current_task_index": 1,
        "desired_task_count": 2,
        "pending_task_count": 0,
        "running_task_count": 2,
        "current_task_id": TASK_ID,
        "running_task_ids": ["123", TASK_ID],
        "family_name": "Task",
    }


def test_arn_to_id() -> None:
    assert TASK_ID == EcsClusterManager.arn_to_id(VALID_ARN)


def test_get_task_info() -> None:
    cluster_manager = setup_ecm()
    asyncio.run(async_test(cluster_manager))


def test_get_current_task_index_empty() -> None:
    assert 0 == EcsClusterManager()._get_current_task_index([])


def test_get_current_task_index_missing() -> None:
    assert -1 == EcsClusterManager()._get_current_task_index([{"taskArn": "123"}])


def test_get_current_task_index_happy() -> None:
    ecm = EcsClusterManager()
    ecm._task_arn = VALID_ARN
    assert 1 == ecm._get_current_task_index(
        [
            {"taskArn": "123"},
            {"taskArn": VALID_ARN},
        ]
    )


def test_get_task_info_without_caching() -> None:
    cluster_manager = setup_ecm()

    asyncio.run(async_test(cluster_manager))
    asyncio.run(async_test(cluster_manager))

    assert cluster_manager._get_running_ecs_tasks.call_count == 2  # type: ignore


def test_get_task_info_valid_cache() -> None:
    cluster_manager = setup_ecm(task_info_cache_sec=1)

    asyncio.run(async_test(cluster_manager))
    asyncio.run(async_test(cluster_manager))

    assert cluster_manager._get_running_ecs_tasks.call_count == 1  # type: ignore


def test_get_task_info_expired_cache() -> None:
    cluster_manager = setup_ecm(task_info_cache_sec=1)

    asyncio.run(async_test(cluster_manager))
    time.sleep(1)
    asyncio.run(async_test(cluster_manager))

    assert cluster_manager._get_running_ecs_tasks.call_count == 2  # type: ignore
