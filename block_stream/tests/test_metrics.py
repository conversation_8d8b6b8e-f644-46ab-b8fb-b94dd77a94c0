"""Unit tests for metrics."""

import json
from typing import Any
from unittest.mock import MagicMock, patch

from block_stream.utils import metrics


@patch("time.time")
def test_all_metrics(mock_time: MagicMock, snapshot: Any) -> None:
    # time.time() is called by the rate metrics on init and on every flush
    # to calculate the time delta. Set a mock value for consistent results.
    mock_time.return_value = 1.0

    multi_lag_metric = metrics.MultiLagMetric(fields=["field1", "field2"])
    multi_rate_metric = metrics.MultiRateMetric(fields=["field1", "field2"])
    multi_num_metric = metrics.MultiNumberMetric(fields=["field1", "field2"])
    multi_callable_metric = metrics.MultiCallableMetric(
        fields=["field1", "field2"], callable_=lambda: {("value1", "value2"): 1.0}
    )
    rate_metric = metrics.RateMetric()
    callable_metric = metrics.CallableMetric(callable_=lambda: 1.0)

    metric_worker = metrics.MetricWorker(
        store_frequency_s=1,
        metrics={
            "multi_lag_metric": multi_lag_metric,
            "multi_rate_metric": multi_rate_metric,
            "multi_num_metric": multi_num_metric,
            "multi_callable_metric": multi_callable_metric,
            "rate_metric": rate_metric,
            "callable_metric": callable_metric,
        },
        component_name="test",
    )

    multi_lag_metric.add(t_ns=1e9, t_now_ns=2e9, field1="value1", field2="value2")
    multi_rate_metric.add(field1="value1", field2="value2")
    multi_num_metric.add(num=1, field1="value1", field2="value2")
    rate_metric.add()

    # Increment time to 2.0 for the first metric flush.
    mock_time.return_value = 2.0

    metric_values = metric_worker._get_metrics_cloudwatch()
    assert len(metric_values) == 6

    snapshot.assert_match(json.dumps(metric_values, indent="  "), "metric_values.json")


def test_callable_metric_error() -> None:
    callable_metric = metrics.CallableMetric(callable_=lambda: 1 / 0)
    multi_callable_metric = metrics.MultiCallableMetric(
        fields=["field1", "field2"],
        callable_=lambda: {("value1", "value2"): 1 / 0},
    )

    metric_worker = metrics.MetricWorker(
        store_frequency_s=1,
        metrics={
            "callable_metric": callable_metric,
            "multi_callable_metric": multi_callable_metric,
        },
        component_name="test",
    )

    metric_values = metric_worker._get_metrics_cloudwatch()
    assert len(metric_values) == 0


def test_consecutive_metrics(snapshot: Any) -> None:
    multi_lag_metric = metrics.MultiLagMetric(fields=["field1", "field2"])

    metric_worker = metrics.MetricWorker(
        store_frequency_s=1,
        metrics={
            "multi_lag_metric": multi_lag_metric,
        },
        component_name="test",
    )

    for i in range(10):
        multi_lag_metric.add(
            t_ns=1e9, t_now_ns=(i + 1) * 1e9, field1="value1", field2="value2"
        )

    metric_values = metric_worker._get_metrics_cloudwatch()
    assert len(metric_values) == 1

    snapshot.assert_match(json.dumps(metric_values, indent="  "), "metric_values.json")
