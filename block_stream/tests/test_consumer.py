import asyncio
import re
import time
from collections.abc import AsyncIterator, Callable
from concurrent.futures import BrokenExecutor, Future, ProcessPoolExecutor
from datetime import UTC, datetime, timedelta
from functools import partial
from typing import (
    Any,
    ParamSpec,
    TypeVar,
    get_args,
)
from unittest.mock import AsyncMock, Mock

import pandas as pd
import pytest

import block_stream.consumer
from block_stream.channel import Channel
from block_stream.consumer.cache import (
    <PERSON>culationCache,
    HistoricalTickCache,
    LastTickCache,
    WaitForSetCalculationCache,
)
from block_stream.consumer.consumer import (
    Consumer,
    ConsumerPool,
    _put_output,
    _run_calc,
)
from block_stream.consumer.typings import ConsumerMode
from block_stream.typings import CatalogData

CHANNEL_STUB_TICK_QFN = "exchange.asset_class.instrument.tick.px"
SECOND_QFN = "2nd" + CHANNEL_STUB_TICK_QFN
THIRD_QFN = "3rd" + CHANNEL_STUB_TICK_QFN


@pytest.mark.asyncio(scope="class")
class ChannelStub(Channel):
    output: list[Any]

    def __init__(
        self,
        data: list[Any] | None = None,
        tick_count: int | None = None,
        delay: float = 0.2,
    ):
        assert not (
            data and tick_count
        ), "Either data or tick_count must be specified, but noth both"

        self.output = []
        self._data = data
        self._tick_count = tick_count
        self._delay = delay

    async def __aiter__(self) -> AsyncIterator[Any]:
        if self._data:
            for d in self._data:
                yield d
                await asyncio.sleep(self._delay)
        elif self._tick_count:
            for _ in range(self._tick_count):
                yield {
                    "q": CHANNEL_STUB_TICK_QFN,
                    "t": int(datetime.now(UTC).timestamp() * 1e9),
                    "v": 1.0,
                }
                await asyncio.sleep(self._delay)

    async def put(self, record: dict[str, Any], key: str = "default") -> bool:
        self.output.append(record)
        return True

    async def flush(self) -> bool:
        return True


@pytest.mark.parametrize("mode", get_args(ConsumerMode))
def test_consumer_new_instrument(mode: ConsumerMode) -> None:
    dpm_instrument: CatalogData = {
        "q": "bybit.option.contracts",
        "instrument": "BTC-28SEP22-20000-C",
        "baseAsset": "BTC",
        "quoteAsset": "USDC",
        "availableSince": "2022-09-26T08:00:00.000Z",
        "listing": "2022-09-26T08:00:00.000Z",
        "expiry": "2022-09-28T08:00:00.000Z",
        "type": "C",
        "strike": 20000.0,
    }
    cache = LastTickCache()
    calc_cache: CalculationCache | WaitForSetCalculationCache
    if mode == "wait-for-set":
        calc_cache = WaitForSetCalculationCache(
            caches=[cache], name="", set_to_wait_for={"foo"}
        )
    else:
        calc_cache = CalculationCache(caches=[cache], name="")
    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(data=[dpm_instrument]),
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=1,
        caches=[calc_cache],
        prep_data=_do_nothing,
        process_chunk_helper=_do_nothing,
    )

    asyncio.run(consumer._async_run())

    assert cache.get_data().get("instruments") == {
        "bybit.option.BTC-28SEP22-20000-C": {
            "qualified_name": "bybit.option.contracts",
            "instrument_name": "BTC-28SEP22-20000-C",
            "instrument": "BTC-28SEP22-20000-C",
            "baseAsset": "BTC",
            "quoteAsset": "USDC",
            "availableSince": "2022-09-26T08:00:00.000Z",
            "listing": "2022-09-26T08:00:00.000Z",
            "expiry": "2022-09-28T08:00:00.000Z",
            "type": "C",
            "strike": 20000.0,
        }
    }


@pytest.mark.parametrize("mode", get_args(ConsumerMode))
def test_consumer_parse_ticks(mode: ConsumerMode) -> None:
    tick_count = 3
    calc_cache: CalculationCache | WaitForSetCalculationCache
    cache = HistoricalTickCache(time_window=timedelta(days=1))
    if mode == "wait-for-set":
        calc_cache = WaitForSetCalculationCache(
            caches=[cache], name="", set_to_wait_for={CHANNEL_STUB_TICK_QFN}
        )
    else:
        calc_cache = CalculationCache(caches=[cache], name="")
    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(tick_count=tick_count),
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=1,
        caches=[calc_cache],
        prep_data=_do_nothing,
        process_chunk_helper=_do_nothing,
    )

    asyncio.run(consumer._async_run())

    assert len(cache.get_data()["raw_data"]) == tick_count


@pytest.mark.parametrize("mode", get_args(ConsumerMode))
def test_consumer_fire_calculation(mode: ConsumerMode) -> None:
    """
    This tests want to ensure the right number of calculation are performed so the
    delays have been carefully chosen
    """

    tick_count = 3
    snapshot_duration_secs = 1  # smaller values may cause the test to flake
    calc_cache = CalculationCache(caches=[LastTickCache()], name="")

    if mode == "timestamp":
        consumer_frequency_ns = (
            1  # this ensures that every tick is processed immediately
        )
    elif mode == "wait-for-set":
        consumer_frequency_ns = (
            1  # this ensures that every tick is processed immediately
        )
        calc_cache = WaitForSetCalculationCache(
            caches=[LastTickCache()], name="", set_to_wait_for={CHANNEL_STUB_TICK_QFN}
        )

    elif mode == "fixed":
        consumer_frequency_ns = int(snapshot_duration_secs * 1e9)
    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(
            tick_count=tick_count, delay=snapshot_duration_secs
        ),  # ticks will be parsed at snapshot_duration_secs frequency
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=consumer_frequency_ns,
        caches=[calc_cache],
        prep_data=_do_nothing,
        process_chunk_helper=_do_nothing,
    )
    consumer._run_calc_and_put_output = AsyncMock()  # type: ignore

    asyncio.run(consumer._async_run())

    assert consumer._run_calc_and_put_output.call_count == tick_count


@pytest.mark.parametrize("mode", get_args(ConsumerMode))
@pytest.mark.parametrize(
    "test_duration_ms, calc_frequency_ms, lag_calc_ms, expected_calc_executions, expected_calc_skips, process_workers",
    [
        (1000, 100, 50, 10, 0, 1),  # Run always
        (1000, 100, 150, 10, 5, 1),  # Run in ms 0, 200, 400, 600, 800
        (1000, 100, 150, 10, 0, 2),  # Run always
        (1000, 100, 210, 10, 6, 1),  # Run in ms 0, 300, 600, 900
        (1000, 100, 210, 10, 3, 2),  # Run in ms 0, 100, 300, 400, 600, 700, 900
        (1000, 100, 450, 10, 8, 1),  # Run in ms 0, 500
        (1000, 100, 450, 10, 6, 2),  # Run in ms 0, 100, 500, 600
    ],
)
def test_consumer_skip_calc_when_lagging(
    mode: ConsumerMode,
    test_duration_ms: int,
    calc_frequency_ms: int,
    lag_calc_ms: int,
    expected_calc_executions: int,
    expected_calc_skips: int,
    process_workers: int,
) -> None:
    """
    This tests want to ensure the right number of calculation are executed based on
    calculation lags and number of available workers
    """

    # Set num of workers
    block_stream.consumer.consumer.NUM_WORKERS = process_workers  # type: ignore
    cache = CalculationCache(caches=[LastTickCache()], name="testing")
    if mode == "timestamp" or mode == "wait-for-set":
        consumer_frequency_ns = (
            1  # this ensures that every tick is processed immediately
        )
        # Send ticks per expected calc executions
        tick_count = expected_calc_executions
        # Delay each tick depending on the test duration and number of expected executions
        tick_delay_sec = (test_duration_ms / expected_calc_executions) / 1e3
        cache = WaitForSetCalculationCache(
            caches=[LastTickCache()],
            name="testing",
            set_to_wait_for={CHANNEL_STUB_TICK_QFN},
        )
    elif mode == "fixed":
        tick_count = 1
        tick_delay_sec = test_duration_ms / 1e3  # tick once and wait to finish test

        # frequency to run the calculation the exact number of times
        consumer_frequency_ns = int((test_duration_ms / expected_calc_executions) * 1e6)
    else:
        raise NotImplementedError

    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(
            tick_count=tick_count, delay=tick_delay_sec
        ),  # ticks will be parsed at snapshot_duration_secs frequency
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=consumer_frequency_ns,
        caches=[cache],
        prep_data=partial(_add_lag_call, lag_ms=lag_calc_ms),
        process_chunk_helper=_do_nothing,
    )

    consumer._fire_and_forget_calc = Mock(side_effect=consumer._fire_and_forget_calc)  # type: ignore
    consumer._run_calc_and_put_output = AsyncMock(side_effect=consumer._run_calc_and_put_output)  # type: ignore

    asyncio.run(consumer._async_run())

    assert consumer._fire_and_forget_calc.call_count == expected_calc_executions
    assert consumer._run_calc_and_put_output.call_count == (
        expected_calc_executions - expected_calc_skips
    )


@pytest.mark.xfail(reason="This test is flaky")
@pytest.mark.parametrize("mode", get_args(ConsumerMode))
def test_consumer_is_not_blocked(mode: ConsumerMode) -> None:
    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(tick_count=2),
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=1,
        caches=[
            WaitForSetCalculationCache(
                caches=[LastTickCache()],
                name="",
                set_to_wait_for={CHANNEL_STUB_TICK_QFN},
            )
        ],
        prep_data=_blocking_call,
        process_chunk_helper=_blocking_call,
    )

    start = time.time()
    asyncio.run(consumer._async_run())
    end = time.time()

    assert (end - start) < 1, "_blocking_call() blocked the Consumer"


@pytest.mark.parametrize("use_dataframe", [True, False])
def test_put_output(use_dataframe: bool) -> None:
    output = [
        {
            "qualified_name": "exchange.asset_class.instrument.tick.px",
            "timestamp": 1700076035000000000,
            "px": 1.0,
        }
    ]
    channel_stub = ChannelStub()

    if use_dataframe:
        asyncio.run(_put_output(channel_stub, pd.DataFrame(output)))
    else:
        asyncio.run(_put_output(channel_stub, output))

    assert channel_stub.output == [
        {
            "q": "exchange.asset_class.instrument.tick.px",
            "t": 1700076035000000000,
            "v": 1.0,
        }
    ]


def test_run_calc_arguments() -> None:
    timestamp = 1234
    chunk_data = {"data": "test"}
    prep_data_mock = Mock(return_value=chunk_data)
    process_chunk_mock = Mock()
    cache_data = {"raw_data": "test"}

    _run_calc(
        name="",
        timestamp=timestamp,
        prep_data=prep_data_mock,
        process_chunk_helper=process_chunk_mock,
        cache_data=cache_data,
    )

    prep_data_mock.assert_called_once_with(snapshot_ts=timestamp, **cache_data)
    process_chunk_mock.assert_called_once_with(
        chunk=chunk_data, snapshot_ts=timestamp, **cache_data
    )


# globally defined so it can be pickled
def _do_nothing(**_: Any) -> list[Any]:
    return []


# globally defined so it can be pickled
def _blocking_call(**_: Any) -> list[Any]:
    # sleep enough time to ensure that the ticks have been processed
    time.sleep(5)
    return []


# globally defined so it can be pickled
def _add_lag_call(lag_ms: int, **_: Any) -> list[Any]:
    time.sleep(lag_ms / 1e3)
    return []


_P = ParamSpec("_P")
_T = TypeVar("_T")


class PoolStub(ConsumerPool):
    def shutdown(self, wait: bool = True) -> None:
        pass

    def submit(
        self, fn: Callable[_P, _T], /, *args: _P.args, **kwargs: _P.kwargs
    ) -> Future[_T]:
        raise BrokenExecutor()


@pytest.mark.parametrize("mode", get_args(ConsumerMode))
def test_broken_executor_handling(mode: ConsumerMode) -> None:
    consumer = Consumer(
        consumer_name="Test Consumer",
        input_data_stream=ChannelStub(tick_count=1),
        output_data_stream=ChannelStub(),
        mode=mode,
        frequency_ns=1,
        caches=[
            WaitForSetCalculationCache(
                caches=[LastTickCache()],
                name="",
                set_to_wait_for={CHANNEL_STUB_TICK_QFN},
            )
        ],
        prep_data=_do_nothing,
        process_chunk_helper=_do_nothing,
    )

    consumer._pool = PoolStub()

    asyncio.run(consumer._async_run())
    # Verify that the pool has been replaced
    assert isinstance(consumer._pool, ProcessPoolExecutor)


@pytest.mark.asyncio(scope="class")
class TestRunWaitForSetDownsampler:
    async def _run_wait_for_set_consumer_downsampler(
        self,
        stream: ChannelStub,
        cache: WaitForSetCalculationCache,
        set_to_wait_for: set[str] | None = None,
    ) -> Consumer:
        consumer = Consumer(
            consumer_name="Test Consumer",
            input_data_stream=stream,
            output_data_stream=ChannelStub(),
            mode="wait-for-set",
            frequency_ns=int(5 * 1e9),
            caches=[cache],
            prep_data=_do_nothing,
            process_chunk_helper=_do_nothing,
        )
        consumer._run_calc_and_put_output = AsyncMock()  # type: ignore
        consumer._fire_and_forget_calc = AsyncMock()  # type: ignore
        await consumer._run_wait_for_set_downsampler()

        return consumer

    async def test_consumer_fire_multiple_times(self) -> None:
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(tick_count=2, delay=1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )

        assert consumer._fire_and_forget_calc.call_count == 2  # type: ignore

    async def test_consumer_fire_multiple_qfn(self) -> None:
        data = [
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int(datetime.now(UTC).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": SECOND_QFN,
                "t": int(datetime.now(UTC).timestamp() * 1e9),
                "v": 1.0,
            },
        ]

        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(data=data, delay=1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )

        assert consumer._fire_and_forget_calc.call_count == 1  # type: ignore

    async def test_consumer_dont_fire_multiple_qfns(self) -> None:
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(tick_count=1, delay=1),
            WaitForSetCalculationCache(
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
                set_to_wait_for={CHANNEL_STUB_TICK_QFN, SECOND_QFN},
            ),
        )
        assert consumer._fire_and_forget_calc.call_count == 0  # type: ignore

    async def test_consumer_fire_multiple_qfns_multiple_timestamps(self) -> None:
        data = [
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((datetime.now(UTC) - timedelta(seconds=10)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int(datetime.now(UTC).timestamp() * 1e9),  # NEWER TIMESTAMP
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int(
                    ((datetime.now(UTC) - timedelta(seconds=10)).timestamp()) * 1e9
                ),
                "v": 1.0,
            },
        ]
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(data=data, delay=0.1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN, SECOND_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )
        assert consumer._fire_and_forget_calc.call_count == 1  # type: ignore

    async def test_consumer_flushes_incomplete_set(
        self, caplog: pytest.LogCaptureFixture
    ) -> None:
        data = [
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((datetime.now(UTC) - timedelta(seconds=10)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((datetime.now(UTC) + timedelta(seconds=60)).timestamp() * 1e9),
                "v": 1.0,
            },
        ]
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(data=data, delay=0.1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN, SECOND_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )
        assert consumer._fire_and_forget_calc.call_count == 1  # type: ignore
        assert (
            re.search(r"Firing .+ with the following MISSING qn's", caplog.text)
            is not None
        )

    async def test_consumer_doesnt_flush_stale_incomplete_set(
        self, caplog: pytest.LogCaptureFixture
    ) -> None:
        now = datetime.now(UTC)
        data = [
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((now - timedelta(seconds=1)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": SECOND_QFN,
                "t": int(now.timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int(now.timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((now + timedelta(seconds=5)).timestamp() * 1e9),
                "v": 1.0,
            },
        ]
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(data=data, delay=0.1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN, SECOND_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )
        assert (
            consumer._fire_and_forget_calc.call_count == 1  # type: ignore
        )  # (executes message 2 & 3)
        # msg 4 timestamp deletes msg 1
        assert "Deleting stale incomplete set to wait for" in caplog.text

    async def test_consumer_random_chronological_trigger_order(
        self, caplog: pytest.LogCaptureFixture
    ) -> None:
        now = datetime.now(UTC)
        data = [
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((now - timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": SECOND_QFN,
                "t": int(now.timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int((now + timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": CHANNEL_STUB_TICK_QFN,
                "t": int(now.timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": SECOND_QFN,
                "t": int((now - timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": THIRD_QFN,  # FIRST COMPLETE SET
                "t": int((now - timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": THIRD_QFN,  # SECOND COMPLETE SET
                "t": int(now.timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": SECOND_QFN,
                "t": int((now + timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
            {
                "q": THIRD_QFN,  # THIRD COMPLETE SET
                "t": int((now + timedelta(seconds=2)).timestamp() * 1e9),
                "v": 1.0,
            },
        ]
        consumer = await self._run_wait_for_set_consumer_downsampler(
            ChannelStub(data=data, delay=0.1),
            WaitForSetCalculationCache(
                set_to_wait_for={CHANNEL_STUB_TICK_QFN, SECOND_QFN, THIRD_QFN},
                caches=[HistoricalTickCache(time_window=timedelta(days=1))],
                name="",
            ),
        )

        assert "Deleting stale incomplete set to wait for" not in caplog.text
        assert "Firing {} with the following missing qn's" not in caplog.text
        assert (
            re.search(r"Firing .+ with the following missing qn's", caplog.text) is None
        )
        assert consumer._fire_and_forget_calc.call_count == 3  # type: ignore
