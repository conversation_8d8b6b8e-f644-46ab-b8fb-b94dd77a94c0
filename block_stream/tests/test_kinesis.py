import asyncio
import json
from collections.abc import Async<PERSON>terator, Callable
from datetime import UTC, datetime, timedelta
from typing import (
    Any,
    Literal,
)
from unittest.mock import AsyncMock, patch

import pytest
import zstd
from botocore.exceptions import ClientError
from types_aiobotocore_kinesis import <PERSON>nesisClient

from block_stream.kinesis import <PERSON><PERSON><PERSON>
from block_stream.typings import CONSUMER_MODE, OUTPUT_MODE


@pytest.mark.asyncio(scope="class")
class KinesisClientStub(KinesisClient):
    def __init__(
        self,
        available_streams: list[str],
        fanout_consumer_by_streams: dict[str, int] | None = None,
        creation_lag_sec: int | None = None,
    ):
        self._available_streams = available_streams
        self._stream_creation_datetime = datetime.now(UTC)
        self._fanout_consumer_by_streams = (
            fanout_consumer_by_streams if fanout_consumer_by_streams else {}
        )
        if creation_lag_sec:
            self._stream_creation_datetime = self._stream_creation_datetime - timedelta(
                seconds=creation_lag_sec
            )

    async def list_streams(self, **_: Any) -> Any:
        return {
            "StreamSummaries": [
                {
                    "StreamName": s,
                    "StreamStatus": "ACTIVE",
                    "StreamARN": s,
                    "StreamCreationTimestamp": self._stream_creation_datetime,
                }
                for s in self._available_streams
            ]
        }

    async def list_stream_consumers(self, StreamARN: str, **_: Any) -> Any:
        return {
            "Consumers": [
                {"StreamName": f"test_{idx}", "ConsumerStatus": "ACTIVE"}
                for idx in range(self._fanout_consumer_by_streams.get(StreamARN, 0))
            ]
        }

    async def describe_stream(self, StreamName: str = "", **_: Any) -> Any:
        if StreamName not in self._available_streams:
            raise ClientError(
                error_response={"Error": {"Code": "UnknownOperationException"}},
                operation_name="TestingClientException",
            )
        return {
            "StreamDescription": {
                "StreamName": StreamName,
                "StreamARN": StreamName,
                "StreamStatus": "ACTIVE",
            },
        }

    async def register_stream_consumer(
        self, *, StreamARN: str, ConsumerName: str
    ) -> Any:
        if StreamARN not in self._available_streams:
            raise ClientError(
                error_response={"Error": {"Code": "UnknownOperationException"}},
                operation_name="TestingClientException",
            )
        if self._fanout_consumer_by_streams.get(StreamARN, 0) >= 20:
            raise ClientError(
                error_response={"Error": {"Code": "LimitExceededException"}},
                operation_name="TestingClientException",
            )
        return {
            "Consumer": {
                "ConsumerName": StreamARN,
                "ConsumerARN": StreamARN,
                "ConsumerStatus": "ACTIVE",
                "ConsumerCreationTimestamp": datetime,
            }
        }


@pytest.mark.parametrize(
    "mode, stream_name, all_stream_names, output_stream_names_filter, expected_output_streams",
    [
        (
            "single",
            "tickData_stream",
            [
                "tickData_stream",
                "tickData_stream_1",
                "tickData_stream_2",
                "internal_stream_4",
            ],
            None,
            ["tickData_stream"],
        ),
        (
            "multiple",
            "tickData_stream",
            [
                "tickData_stream",
                "tickData_stream_1",
                "tickData_stream_2",
                "internal_stream_4",
            ],
            None,
            ["tickData_stream", "tickData_stream_1", "tickData_stream_2"],
        ),
        (
            "multiple",
            "tickData_stream_3",
            ["tickData_stream_1", "tickData_stream_2", "internal_stream_4"],
            None,
            [],
        ),
        (
            "multiple",
            "tickData_stream",
            [
                "tickData_stream",
                "tickData_stream_1",
                "tickData_stream_2",
                "internal_stream_2",
                "internal_stream_4",
            ],
            lambda x: x.startswith("internal_stream"),
            ["internal_stream_2", "internal_stream_4"],
        ),
    ],
)
def test_kinesis_update_output_streams(
    mode: OUTPUT_MODE,
    stream_name: str,
    all_stream_names: list[str],
    output_stream_names_filter: Callable[[str], bool] | None,
    expected_output_streams: list[str],
) -> None:
    transport_layer = Kinesis(
        stream_name=stream_name,
        app_name="test_app",
        shard_index=0,
        output_mode=mode,
        output_stream_names_filter=output_stream_names_filter,
    )

    transport_layer._get_client = AsyncMock(  # type: ignore
        return_value=KinesisClientStub(available_streams=all_stream_names)
    )
    asyncio.run(transport_layer.update_output_stream_list())

    assert transport_layer._output_stream_names == expected_output_streams


@pytest.mark.asyncio
async def test_get_shard_simple() -> None:
    transport_layer = Kinesis(
        stream_name="test_stream",
        app_name="test_app",
        shard_index=0,
    )
    with patch.object(
        transport_layer, "_get_client", new_callable=AsyncMock
    ) as mock_get_client:
        with patch.object(
            transport_layer, "_list_shards", new_callable=AsyncMock
        ) as mock_list_shards:
            mock_get_client.return_value = AsyncMock()
            mock_list_shards.return_value = [
                {
                    "ShardId": "shard-0001",
                    "HashKeyRange": {
                        "StartingHashKey": "0",
                        "EndingHashKey": "340282366920938463463374607431768211455",
                    },
                    "SequenceNumberRange": {
                        "StartingSequenceNumber": "49596814390654349135278358629431837932688673288987201538"
                    },
                },
                {
                    "ShardId": "shard-0002",
                    "HashKeyRange": {
                        "StartingHashKey": "340282366920938463463374607431768211456",
                        "EndingHashKey": "680564733841876926926749214863536422911",
                    },
                    "SequenceNumberRange": {
                        "StartingSequenceNumber": "49596814390654349135278358629431837932688673288987201539"
                    },
                },
            ]

            shard = await transport_layer.get_shard()
            assert shard["ShardId"] == "shard-0001"


@pytest.mark.asyncio
async def test_get_shard_out_of_range() -> None:
    transport_layer = Kinesis(
        stream_name="test_stream",
        app_name="test_app",
        shard_index=5,
    )
    with patch.object(transport_layer, "_get_client", new_callable=AsyncMock):
        with patch.object(transport_layer, "_list_shards", new_callable=AsyncMock):

            with pytest.raises(RuntimeError, match="Shard #5 does not exist"):
                await transport_layer.get_shard()


class MockEventStream:
    def __init__(self, events: list[dict[str, Any]]):
        self.events = events
        self.index = 0

    def __aiter__(self) -> AsyncIterator[Any]:
        return self

    async def __anext__(self) -> dict[str, Any]:
        if self.index < len(self.events):
            event = self.events[self.index]
            self.index += 1
            return event
        raise StopAsyncIteration


@pytest.fixture
def mock_kinesis_client() -> AsyncMock:
    mock_client = AsyncMock()
    mock_client.subscribe_to_shard.return_value = {
        "EventStream": MockEventStream(
            [
                {
                    "SubscribeToShardEvent": {
                        "Records": [
                            {
                                "Data": b'{"data": {"value": 123}}',
                                "SequenceNumber": "1",
                            },
                            {
                                "Data": b'{"batch": [{"value": 456}, {"value": 789}]}',
                                "SequenceNumber": "2",
                            },
                        ],
                        "ContinuationSequenceNumber": "3",
                        "MillisBehindLatest": 1000,
                    }
                }
            ]
        )
    }
    return mock_client


@pytest.fixture
def mock_checkpointer() -> AsyncMock:
    checkpointer = AsyncMock()
    checkpointer.get_last_checkpoint.return_value = None
    checkpointer.save_checkpoint.return_value = None
    return checkpointer


@pytest.fixture
def base_kinesis_transport_layer(
    monkeypatch: pytest.MonkeyPatch,
    mock_checkpointer: AsyncMock,
) -> Callable[[bool, CONSUMER_MODE, AsyncMock], Kinesis]:
    """
    Base fixture for creating a Kinesis transport layer.

    :param include_sequence_number: Whether to include sequence numbers.
    :param consumer_mode: The consumer mode ("fanout" or "unregistered").
    :param mock_kinesis_client: The appropriate mocked Kinesis client.
    :return: Configured Kinesis transport layer instance.
    """

    def _create_kinesis(
        include_sequence_number: bool,
        consumer_mode: CONSUMER_MODE,
        mock_kinesis_client: AsyncMock,
    ) -> Kinesis:
        if consumer_mode == "fanout":
            monkeypatch.setattr("block_stream.kinesis.IS_ECS", True)
            monkeypatch.setattr("block_stream.kinesis.ECS_INDEX_OVERRIDE", True)

        transport_layer = Kinesis(
            stream_name="test_stream",
            app_name="test_app",
            shard_index=0,
            consumer_mode=consumer_mode,
            include_sequence_number=include_sequence_number,
            checkpointer=mock_checkpointer,
        )

        monkeypatch.setattr(
            transport_layer, "_get_client", AsyncMock(return_value=mock_kinesis_client)
        )
        monkeypatch.setattr(
            transport_layer,
            "get_shard",
            AsyncMock(return_value={"ShardId": "shard-0001"}),
        )

        if consumer_mode == "fanout":
            monkeypatch.setattr(
                transport_layer,
                "_initialise_fanout_consumer",
                AsyncMock(
                    return_value={
                        "arn": "test-consumer-arn",
                        "name": "test-consumer-name",
                    }
                ),
            )
            monkeypatch.setattr(
                transport_layer, "_create_clean_dangling_consumer_tasks", AsyncMock()
            )

        return transport_layer

    return _create_kinesis


@pytest.fixture
def configure_kinesis_transport_layer(
    base_kinesis_transport_layer: Callable[[bool, str, AsyncMock], Kinesis],
    mock_kinesis_client: AsyncMock,
) -> Callable[[bool], Kinesis]:

    return lambda include_sequence_number: base_kinesis_transport_layer(
        include_sequence_number, "fanout", mock_kinesis_client
    )


@pytest.fixture
def configure_kinesis_transport_layer_unregistered(
    base_kinesis_transport_layer: Callable[[bool, str, AsyncMock], Kinesis],
    mock_kinesis_client_unregistered: AsyncMock,
) -> Callable[[bool], Kinesis]:

    return lambda include_sequence_number: base_kinesis_transport_layer(
        include_sequence_number, "unregistered", mock_kinesis_client_unregistered
    )


@pytest.mark.asyncio
async def test_iterate_records_fanout_kinesis_transport_layer(
    configure_kinesis_transport_layer: Any,
) -> None:
    transport_layer = configure_kinesis_transport_layer(include_sequence_number=False)

    records = []
    async for record in transport_layer.iterate_records(
        datetime.now(UTC), start_sequence_number=None
    ):
        records.append(record)
        if len(records) >= 3:
            break

    assert len(records) == 3
    assert records[0] == {"value": 123}
    assert records[1] == {"value": 456}
    assert records[2] == {"value": 789}


@pytest.mark.asyncio
async def test_iterate_records_fanout_include_sequence_number(
    configure_kinesis_transport_layer: Any,
) -> None:
    transport_layer = configure_kinesis_transport_layer(include_sequence_number=True)

    records_with_seq = []
    async for record in transport_layer.iterate_records(
        datetime.now(UTC), start_sequence_number=None
    ):
        assert "sequence_number" in record
        records_with_seq.append(record)
        if len(records_with_seq) >= 3:
            break

    assert len(records_with_seq) == 3
    assert records_with_seq[0] == {"value": 123, "sequence_number": "1"}
    assert records_with_seq[1] == {"value": 456, "sequence_number": "2"}
    assert records_with_seq[2] == {"value": 789, "sequence_number": "2"}


@pytest.fixture
def mock_kinesis_client_unregistered() -> AsyncMock:
    """Mock Kinesis client for unregistered consumer polling (get_records)."""
    mock_client = AsyncMock()
    mock_client.get_records = AsyncMock(
        side_effect=[
            {
                "Records": [
                    {
                        "Data": b'{"data": {"value": 123}}',
                        "SequenceNumber": "1",
                    },
                    {
                        "Data": b'{"batch": [{"value": 456}, {"value": 789}]}',
                        "SequenceNumber": "2",
                    },
                ],
                "NextShardIterator": "next-iterator",
                "MillisBehindLatest": 500,
            },
            {
                "Records": [],
                "NextShardIterator": "next-iterator",
                "MillisBehindLatest": 1000,
            },
        ]
    )
    return mock_client


@pytest.mark.asyncio
async def test_iterate_records_unregistered_kinesis_transport_layer(
    configure_kinesis_transport_layer_unregistered: Any,
) -> None:
    """Test `_iterate_records_unregistered` with polling (no sequence numbers)."""
    transport_layer = configure_kinesis_transport_layer_unregistered(
        include_sequence_number=False
    )

    records = []
    async for record in transport_layer._iterate_records_unregistered(
        datetime.now(UTC)
    ):
        records.append(record)
        if len(records) >= 3:
            break

    assert len(records) == 3
    assert records[0] == {"value": 123}
    assert records[1] == {"value": 456}
    assert records[2] == {"value": 789}


@pytest.mark.asyncio
async def test_iterate_records_unregistered_include_sequence_number(
    configure_kinesis_transport_layer_unregistered: Any,
) -> None:
    """Test `_iterate_records_unregistered` with sequence numbers included."""
    transport_layer = configure_kinesis_transport_layer_unregistered(
        include_sequence_number=True
    )

    records_with_seq = []
    async for record in transport_layer._iterate_records_unregistered(
        datetime.now(UTC)
    ):
        assert "sequence_number" in record
        records_with_seq.append(record)
        if len(records_with_seq) >= 3:
            break

    assert len(records_with_seq) == 3
    assert records_with_seq[0] == {"value": 123, "sequence_number": "1"}
    assert records_with_seq[1] == {"value": 456, "sequence_number": "2"}
    assert records_with_seq[2] == {"value": 789, "sequence_number": "2"}


@pytest.mark.asyncio
async def test_iterate_records_unregistered_backoff(
    configure_kinesis_transport_layer_unregistered: Any,
) -> None:
    """Test `_iterate_records_unregistered` applies exponential backoff on empty records."""
    transport_layer = configure_kinesis_transport_layer_unregistered(
        include_sequence_number=True
    )

    with patch.object(
        transport_layer, "_get_client", new_callable=AsyncMock
    ) as mock_get_client:
        mock_client = mock_get_client.return_value
        mock_client.get_records = AsyncMock(
            side_effect=[
                {
                    "Records": [],
                    "NextShardIterator": "next-iterator",
                    "MillisBehindLatest": 500,
                },  # No records, backoff should apply
                {
                    "Records": [
                        {"Data": b'{"data": {"value": 555}}', "SequenceNumber": "10"}
                    ],
                    "NextShardIterator": "new-iterator",
                    "MillisBehindLatest": 300,
                },
            ]
        )

        records = []
        async for record in transport_layer._iterate_records_unregistered(
            datetime.now(UTC)
        ):
            records.append(record)
            if len(records) >= 1:
                break

        assert len(records) == 1
        assert records[0] == {"value": 555, "sequence_number": "10"}


def dt_utc(y: int, m: int, d: int) -> datetime:
    return datetime(y, m, d, tzinfo=UTC)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "start_seq, checkpoint_seq, timestamp, seconds_per_checkpoint, expected_type, expected_value, raise_exception",
    [
        ("200", "100", None, 60, "AFTER_SEQUENCE_NUMBER", "200", None),
        (None, "150", None, 60, "AFTER_SEQUENCE_NUMBER", "150", None),
        (None, None, dt_utc(2023, 1, 1), 60, "AT_TIMESTAMP", dt_utc(2023, 1, 1), None),
        (None, None, None, None, "LATEST", None, None),
        (None, None, None, 60, "TRIM_HORIZON", None, None),
        ("999", None, dt_utc(2020, 1, 1), 60, "AFTER_SEQUENCE_NUMBER", "999", None),
        (None, "888", dt_utc(2022, 2, 2), 60, "AFTER_SEQUENCE_NUMBER", "888", None),
        ("123", "456", dt_utc(2021, 1, 1), 60, "AFTER_SEQUENCE_NUMBER", "123", None),
        (
            None,
            None,
            dt_utc(2023, 5, 1),
            None,
            "AT_TIMESTAMP",
            dt_utc(2023, 5, 1),
            None,
        ),
        # fallback to TRIM_HORIZON when exception occurs on starting sequence
        ("777", None, None, 60, "TRIM_HORIZON", None, "InvalidArgumentException"),
        (None, "999", None, 60, "TRIM_HORIZON", None, "ResourceNotFoundException"),
    ],
)
async def test_iterator_type_matrix_unregistered_only(
    start_seq: str | None,
    checkpoint_seq: str | None,
    timestamp: datetime | None,
    seconds_per_checkpoint: int | None,
    expected_type: str,
    expected_value: str | datetime | None,
    raise_exception: str | None,
) -> None:
    mock_checkpoint = AsyncMock()
    kinesis = Kinesis(
        stream_name="test_stream",
        app_name="test_app",
        shard_index=0,
        checkpointer=mock_checkpoint,
        seconds_per_checkpoint=seconds_per_checkpoint,
        consumer_mode="unregistered",
    )

    mock_client = AsyncMock()
    call_args = []

    if raise_exception:
        called = [False]

        # ClientError on first call, success on second with TRIM_HORIZON
        async def fake_get_shard_iterator(**kwargs: Any) -> dict[str, Any]:
            if not called[0]:
                called[0] = True
                raise ClientError(
                    error_response={"Error": {"Code": raise_exception}},
                    operation_name="GetShardIterator",
                )
            call_args.append(kwargs)
            return {"ShardIterator": "fake-iterator"}

        mock_client.get_shard_iterator.side_effect = fake_get_shard_iterator
    else:

        async def fake_get_shard_iterator(**kwargs: Any) -> dict[str, Any]:
            call_args.append(kwargs)
            return {"ShardIterator": "fake-iterator"}

        mock_client.get_shard_iterator.side_effect = fake_get_shard_iterator

    if checkpoint_seq:
        mock_checkpoint.get_last_checkpoint.return_value = AsyncMock(
            sequence_number=checkpoint_seq
        )
        kinesis._checkpointer = mock_checkpoint
    elif seconds_per_checkpoint:
        kinesis._checkpointer = AsyncMock()
        kinesis._checkpointer.get_last_checkpoint.return_value = None
    else:
        kinesis._checkpointer = None

    with (
        patch.object(kinesis, "get_shard", return_value={"ShardId": "shard-0001"}),
        patch.object(kinesis, "_get_client", return_value=mock_client),
    ):
        iterator = await kinesis._get_iterator(
            start_timestamp=timestamp,
            start_sequence_number=start_seq,
        )

        assert iterator == "fake-iterator"
        assert len(call_args) >= 1
        args = call_args[-1]

        assert args["ShardIteratorType"] == expected_type
        if expected_type == "AFTER_SEQUENCE_NUMBER":
            assert args["StartingSequenceNumber"] == expected_value
        elif expected_type == "AT_TIMESTAMP":
            assert args["Timestamp"] == expected_value
        elif expected_type == "TRIM_HORIZON":
            assert args["ShardIteratorType"] == "TRIM_HORIZON"
        else:
            assert "StartingSequenceNumber" not in args
            assert "Timestamp" not in args


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "start_seq, timestamp, expected_type, expected_value, record_data",
    [
        (
            "999",
            None,
            "AFTER_SEQUENCE_NUMBER",
            "999",
            {"batch": [{"foo": "bar"}]},
        ),
        (
            None,
            dt_utc(2024, 2, 1),
            "AT_TIMESTAMP",
            dt_utc(2024, 2, 1),
            {"batch": [{"hello": "world"}]},
        ),
        (
            None,
            None,
            "LATEST",
            None,
            {"data": {"foo": "baz"}},
        ),
    ],
)
async def test_iterate_records_fanout_happy_path(
    start_seq: str | None,
    timestamp: datetime | None,
    expected_type: Literal["AFTER_SEQUENCE_NUMBER", "AT_TIMESTAMP", "LATEST"],
    expected_value: str | datetime | None,
    record_data: dict[str, Any],
) -> None:
    """Test fanout consumer with various valid starting positions."""
    sequence_number = "123"
    compressed = zstd.compress(json.dumps(record_data).encode())
    record = {"Data": compressed, "SequenceNumber": sequence_number}

    mock_event_stream = AsyncMock()
    mock_event_stream.__aiter__.return_value = [
        {
            "SubscribeToShardEvent": {
                "Records": [record],
                "MillisBehindLatest": 0,
                "ContinuationSequenceNumber": sequence_number,
            }
        }
    ]

    mock_client = AsyncMock()
    mock_client.subscribe_to_shard.return_value = {"EventStream": mock_event_stream}

    kinesis = Kinesis(
        stream_name="test_stream",
        app_name="test_app",
        shard_index=0,
        consumer_mode="fanout",
        include_sequence_number=True,
    )

    with (
        patch.object(kinesis, "_get_client", return_value=mock_client),
        patch.object(kinesis, "get_shard", return_value={"ShardId": "shard-0001"}),
        patch.object(
            kinesis,
            "_initialise_fanout_consumer",
            return_value={"arn": "arn", "name": "test-consumer"},
        ),
        patch.object(kinesis, "_create_clean_dangling_consumer_tasks"),
    ):
        records = []
        async for r in kinesis._iterate_records_fanout(
            starting_timestamp=timestamp,
            start_sequence_number=start_seq,
        ):
            records.append(r)
            break  # Only test first record

    # Expected payload
    if "data" in record_data:
        expected_payload = record_data["data"]
    elif "batch" in record_data:
        expected_payload = record_data["batch"][0]
    else:
        expected_payload = record_data

    expected_payload["sequence_number"] = sequence_number
    assert records == [expected_payload]

    # Confirm subscribe_to_shard call
    subscribe_args = mock_client.subscribe_to_shard.call_args.kwargs

    assert subscribe_args["ShardId"] == "shard-0001"
    assert subscribe_args["ConsumerARN"] == "arn"
    assert subscribe_args["StartingPosition"]["Type"] == expected_type

    if expected_type in {"AFTER_SEQUENCE_NUMBER", "AT_SEQUENCE_NUMBER"}:
        assert subscribe_args["StartingPosition"]["SequenceNumber"] == expected_value
    elif expected_type == "AT_TIMESTAMP":
        assert subscribe_args["StartingPosition"]["Timestamp"] == expected_value
    else:
        assert "SequenceNumber" not in subscribe_args["StartingPosition"]
        assert "Timestamp" not in subscribe_args["StartingPosition"]


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "error_codes, start_seq, should_retry, expected_first_type, expected_last_type",
    [
        # Cases that should retry and fallback to TRIM_HORIZON
        (
            ["ResourceNotFoundException"],
            "123",
            True,
            "AFTER_SEQUENCE_NUMBER",
            "TRIM_HORIZON",
        ),
        (
            ["InvalidArgumentException"],
            "999",
            True,
            "AFTER_SEQUENCE_NUMBER",
            "TRIM_HORIZON",
        ),
        # Cases that should not retry (no start_seq)
        (["ResourceNotFoundException"], None, False, "LATEST", "LATEST"),
        (["InvalidArgumentException"], None, False, "LATEST", "LATEST"),
        # ResourceInUseException should retry multiple times with same params
        (
            ["ResourceInUseException", "ResourceInUseException"],
            "222",
            True,
            "AFTER_SEQUENCE_NUMBER",
            "AFTER_SEQUENCE_NUMBER",
        ),
        # Mixed error types - ResourceInUseException followed by other errors
        (
            ["ResourceInUseException", "InvalidArgumentException"],
            "222",
            True,
            "AFTER_SEQUENCE_NUMBER",
            "TRIM_HORIZON",
        ),
        (
            ["ResourceInUseException", "ResourceNotFoundException"],
            "222",
            True,
            "AFTER_SEQUENCE_NUMBER",
            "TRIM_HORIZON",
        ),
        # Multiple ResourceInUseException with no sequence number
        (
            ["ResourceInUseException", "ResourceInUseException"],
            None,
            True,
            "LATEST",
            "LATEST",
        ),
        # Mixed errors with no sequence number
        (
            ["ResourceInUseException", "InvalidArgumentException"],
            None,
            True,
            "LATEST",
            "LATEST",
        ),
    ],
)
async def test_iterate_records_fanout_error_cases(
    error_codes: list[str],
    start_seq: str | None,
    should_retry: bool,
    expected_first_type: str,
    expected_last_type: str,
) -> None:
    """Test fanout consumer error handling for various scenarios."""
    sequence_number = "123"
    record_data = {"data": {"fallback": "success"}}
    compressed = zstd.compress(json.dumps(record_data).encode())
    record = {"Data": compressed, "SequenceNumber": sequence_number}

    mock_event_stream = AsyncMock()
    mock_event_stream.__aiter__.return_value = [
        {
            "SubscribeToShardEvent": {
                "Records": [record],
                "MillisBehindLatest": 0,
                "ContinuationSequenceNumber": sequence_number,
            }
        }
    ]

    mock_client = AsyncMock()
    call_count: int = 0

    async def fake_subscribe_to_shard(*args: Any, **kwargs: Any) -> dict[str, Any]:
        nonlocal call_count
        if call_count < len(error_codes):
            call_count += 1
            raise ClientError(
                error_response={"Error": {"Code": error_codes[call_count - 1]}},
                operation_name="SubscribeToShard",
            )
        call_count += 1
        return {"EventStream": mock_event_stream}

    # Mock asyncio.sleep to make tests faster
    async def fake_sleep(*args: Any, **kwargs: Any) -> None:
        pass

    mock_client.subscribe_to_shard.side_effect = fake_subscribe_to_shard

    kinesis = Kinesis(
        stream_name="test_stream",
        app_name="test_app",
        shard_index=0,
        consumer_mode="fanout",
        include_sequence_number=True,
    )

    with (
        patch.object(kinesis, "_get_client", return_value=mock_client),
        patch.object(kinesis, "get_shard", return_value={"ShardId": "shard-0001"}),
        patch.object(
            kinesis,
            "_initialise_fanout_consumer",
            return_value={"arn": "arn", "name": "test-consumer"},
        ),
        patch.object(kinesis, "_create_clean_dangling_consumer_tasks"),
        patch("asyncio.sleep", new=fake_sleep),
    ):
        has_non_resource_in_use_errors = any(
            err != "ResourceInUseException" for err in error_codes
        )
        is_sequence_based_retry = start_seq is not None

        should_raise_error = not should_retry or (  # Explicitly marked as no retry
            not is_sequence_based_retry and has_non_resource_in_use_errors
        )  # No sequence number but has other errors

        if should_raise_error:
            with pytest.raises(ClientError) as excinfo:
                async for _ in kinesis._iterate_records_fanout(
                    start_sequence_number=start_seq,
                ):
                    pass
            # Should get the last non-ResourceInUseException error
            expected_error = next(
                err for err in reversed(error_codes) if err != "ResourceInUseException"
            )
            assert expected_error in str(excinfo.value)
            # Should have retried all ResourceInUseException errors before failing
            expected_calls = (
                sum(1 for err in error_codes if err == "ResourceInUseException") + 1
            )
            assert call_count == expected_calls
        else:
            # All other retry cases
            records = []
            async for r in kinesis._iterate_records_fanout(
                start_sequence_number=start_seq,
            ):
                records.append(r)
                break

            assert len(records) == 1
            assert records[0] == {
                "fallback": "success",
                "sequence_number": sequence_number,
            }
            assert call_count == len(error_codes) + 1  # All errors plus success

            # Verify first call
            first_call = mock_client.subscribe_to_shard.call_args_list[0]
            assert first_call.kwargs["StartingPosition"]["Type"] == expected_first_type
            if start_seq:
                assert (
                    first_call.kwargs["StartingPosition"]["SequenceNumber"] == start_seq
                )

            # Verify last call used expected type
            last_call = mock_client.subscribe_to_shard.call_args_list[-1]
            assert last_call.kwargs["StartingPosition"]["Type"] == expected_last_type

            # If we have ResourceInUseException, verify intermediate calls maintained the same params
            if "ResourceInUseException" in error_codes:
                for i, call in enumerate(
                    mock_client.subscribe_to_shard.call_args_list[:-1]
                ):
                    if error_codes[i] == "ResourceInUseException":
                        assert (
                            call.kwargs["StartingPosition"]["Type"]
                            == expected_first_type
                        )
                        if start_seq:
                            assert (
                                call.kwargs["StartingPosition"]["SequenceNumber"]
                                == start_seq
                            )
