import asyncio

from block_stream.utils.locks import <PERSON>zy<PERSON><PERSON>t<PERSON>ock


def test_lazy_lock() -> None:
    # If we are not in the main thread, we need to explicitly create an event
    # loop prior to initializing the Lock.
    asyncio.set_event_loop(asyncio.new_event_loop())

    # No longer raising with nest-asyncio used in the project
    # with pytest.raises(RuntimeError):
    #     _test_lock(lock)

    # No exception with lazy lock.
    lazy_lock = LazyInitLock()
    _test_lock(lazy_lock)


def _test_lock(lock: LazyInitLock) -> None:
    async def _test() -> None:
        await asyncio.gather(
            locker(),
            locker(),
        )

    async def locker() -> None:
        async with lock:
            await asyncio.sleep(1)

    asyncio.run(_test())
