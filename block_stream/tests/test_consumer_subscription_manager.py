import asyncio
from typing import Any
from unittest.mock import AsyncMock, Mock

import pytest

from block_stream import ConsumerSubscriptionManager
from block_stream.consumer.consumer_subscription_manager import (
    ConsumerBaseFunctionClass,
    ConsumerSubMessage,
    Subscriber,
)


@pytest.mark.parametrize(
    "desired,pending,running,current,fn_id,expected",
    [
        (1, 0, 1, 0, "0", True),
        (2, 0, 2, 0, "1", False),
        (2, 0, 2, 1, "1", True),
        (2, 1, 2, 1, "1", True),
        (2, 1, 2, 1, "0", False),
    ],
)
def test_should_handle_fn_fan_out(
    desired: int, pending: int, running: int, current: int, fn_id: str, expected: bool
) -> None:
    class DummyCSM(
        ConsumerSubscriptionManager[
            ConsumerBaseFunctionClass[ConsumerSubMessage, Subscriber],
            ConsumerSubMessage,
        ]
    ):
        _cluster_manager = Mock()
        _cluster_manager.get_task_info = AsyncMock(
            return_value={
                "desired_task_count": desired,
                "pending_task_count": pending,
                "running_task_count": running,
                "current_task_index": current,
            }
        )

        def __init__(self) -> None:
            self._active_functions = {}

        def on_delete_fn(self) -> Any:
            pass

    async def test() -> None:
        result = await ConsumerSubscriptionManager.should_handle_fn_fan_out(
            DummyCSM(), fn_id
        )
        assert result is expected

    asyncio.run(test())
