from typing import Literal, NotRequired, Required

from typing_extensions import TypedDict

REPLAY_MODE = Literal["record", "replay", "replay_realtime"]
CONSUMER_MODE = Literal["unregistered", "fanout"] | None
OUTPUT_MODE = Literal["single", "multiple"] | None
TIME_PRECISION = Literal["s", "ms", "us", "ns"]


class CatalogData(TypedDict, total=False):
    q: Required[str]
    instrument: Required[str]
    baseAsset: Required[str]
    quoteAsset: Required[str]
    settlementAsset: str
    expiry: str
    availableSince: str
    listing: str
    type: str
    strike: float


class InstrumentDetails(TypedDict, total=False):
    qualified_name: str
    instrument_name: Required[
        str
    ]  # TODO Remove unnecessary conversion once ALL calcs can handle 'instrument'. Catalog is also stored as 'instrument'
    instrument: Required[str]
    baseAsset: Required[str]
    quoteAsset: Required[str]
    availableSince: str
    listing: str
    type: str
    strike: float
    expiry: str


InstrumentsDetailsMap = dict[str, InstrumentDetails]
"""
Keys are in the form "exchange.asset_class.instrument_name"
"""


class ECSTaskDef(TypedDict, total=False):
    Cluster: Required[str]
    Family: Required[str]
    TaskARN: Required[str]
    ServiceName: Required[str]


class CatalogFilter(TypedDict):
    exchanges: list[str]
    asset_class: list[
        Literal[
            "spot",
            "perpetual",
            "option",
            "future",
            "option-equity",
            "future-equity",
            "perpetual-equity",
            "spot-equity",
        ]
    ]
    base_assets: list[str]
    quote_assets: NotRequired[list[str]]
    suffixes: list[str]


class EcsTaskInfo(TypedDict, total=False):
    desired_task_count: int
    pending_task_count: int
    running_task_count: int
    current_task_index: int
    current_task_id: str | None
    running_task_ids: list[str] | None
    family_name: str | None


class FanoutConsumer(TypedDict):
    arn: str
    name: str
