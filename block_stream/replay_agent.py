from datetime import datetime

from block_stream import Agent
from block_stream.channel import Channel
from block_stream.replayer import Replayer
from block_stream.typings import CONSUMER_MODE

"""
Use Cases
    * Replay data from Dynamo but write results on Kinesis Queue(s)
    * Replay data from Dynamo but write results

"""


class ReplayAgent(Agent):
    def channel(  # type: ignore
        self,
        name: str,
        seconds_per_checkpoint: int | None = None,
        custom_stream: str | None = None,
        shard_index: int = 0,
        auto_flush_s: float | None = None,
        consumer_mode: CONSUMER_MODE = None,
        start_timestamp: datetime | None = None,
    ) -> None | Channel | Replayer:
        return None
