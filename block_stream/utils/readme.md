# BlockStream Utils

Collection of utility functions used across streaming components

## Metrics

Provides a set of classes to collect metrics and store them in Cloudwatch.

Example usage:

```python
import os
import asyncio
from block_stream.utils.metrics import MultiLagMetric, MetricWorker, RateMetric, CallableMetric

METRIC_S = int(os.environ.get("METRIC_S", 60))

# Supports measuring lags across multiple dimensions using a single metric
# Fields specified beforehand to avoid creating new dimensions and more
# efficient data collection
lag_metric = MultiLagMetric(fields=["exchange", "asset_class"])

# Measure the rate of an event such as messages sent (number of events/sec)
sent_rate_metric = RateMetric()

# Wrapper for an arbitrary function called every time the metrics are
# evaluated, such as one that returns the number of active connections
connection_metric = CallableMetric(custom_fn)

# Metric worker to define any combination of Metrics particular to the application
metric_worker = MetricWorker(
    METRIC_S,
    {
        "sent_per_s":         sent_rate_metric,
        "lag":                lag_metric,
        "active_connections": connection_metric
    },
    component_name="DpmToBlockStream",
)


async def some_computation(dp):
    # Perform the rest of the computation, and then record the metrics

    lag_metric.add(
        dp["timestamp"],  # Computes lag between timestamp and current time
        exchange=dp["exchange"],
        asset_class=dp["asset_class"]
    )

    sent_rate_metric.add()


async def run():
    # The metric worker needs to run in an async thread along with
    # the rest of the application
    await asyncio.gather(
        ...,
        metric_worker.run(),
    )
```