"""
Profiler Manager

A module that provides CPU and memory profiling capabilities with automatic file rotation
and S3 upload support. Supports both local and ECS environments.

Features:
- Memory profiling using memray
- CPU profiling using yappi
- Automatic file compression and rotation
- S3 upload support
- Child process profiling support
"""

import asyncio
import atexit
import gzip
import logging
import os
import pickle
import shutil
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from ctypes import c_int
from multiprocessing import Value, current_process
from multiprocessing.process import BaseProcess
from multiprocessing.util import register_after_fork
from pathlib import Path
from threading import Lock, Thread
from types import TracebackType
from typing import Dict, Optional, Type

import boto3  # type: ignore
import utils_general
import yappi  # type: ignore
from memray import Tracker  # type: ignore
from pydantic import BaseModel  # type: ignore
from typing_extensions import Self

from block_stream.config import IS_ECS
from block_stream.utils.ecs_cluster_manager import EcsClusterManager


class S3Config(BaseModel):  # type: ignore
    """S3 configuration settings."""

    s3_bucket: str
    s3_prefix: str


class MemrayConfig(BaseModel):  # type: ignore
    """Memory profiler configuration settings."""

    enable: bool = False
    interval_sec: int = 3600


class YappiConfig(BaseModel):  # type: ignore
    """CPU profiler configuration settings using yappi."""

    enable: bool = False
    interval_sec: int = 3600
    clock_type: str = "cpu"  # can be 'cpu' or 'wall'
    profile_threads: bool = True


class ProfilerConfig(BaseModel):  # type: ignore
    """Main profiler configuration."""

    enable: bool = False
    profile_child_process: bool = False
    child_process_count: int = 1
    path: str = ""
    s3: Optional[S3Config] = None
    memray: MemrayConfig = MemrayConfig()
    yappi: YappiConfig = YappiConfig()


# Load configuration from environment
try:
    _PROFILER_CONFIG = ProfilerConfig(
        **utils_general.json_loads(os.environ.get("PROFILER_CONFIG", "{}"))
    )
except Exception as e:
    logging.error(f"Failed to load profiler config: {e}")
    _PROFILER_CONFIG = ProfilerConfig()


def _calculate_next_rotation(current_time: float, interval: int) -> int:
    """Calculate the next rotation time."""
    return int((current_time // interval) * interval) + interval


_tracker: Optional[Tracker] = None


class ProcessProfiler:
    """Handles profiling for a single process."""

    def __init__(
        self,
        pid: int,
        base_path: Path,
        task_id: Optional[str] = None,
        is_main: bool = False,
    ):
        self._pid = pid
        self._base_path = base_path
        self._task_id = task_id if task_id else "local"
        self._running = False
        self._is_main = is_main

        self._memray_tracker: Optional[Tracker] = None
        self._memray_file: Optional[Path] = None
        self._task: Optional[asyncio.Task[None]] = None
        self._rotation_executor = ThreadPoolExecutor(max_workers=2)

        self._yappi_enabled = _PROFILER_CONFIG.yappi.enable
        self._memray_enabled = _PROFILER_CONFIG.memray.enable

        self._s3_client = boto3.client("s3") if IS_ECS else None

    def start(self) -> None:
        """Start profiling this process."""
        if not self._yappi_enabled and not self._memray_enabled:
            logging.info(
                f"Skipping profiling {'main' if self._is_main else 'child'} process. PID: {self._pid}. All tools disabled."
            )
            return

        logging.info(
            f"Starting profiling {'main' if self._is_main else 'child'} process. PID: {self._pid}"
        )

        self._running = True

        # Get main thread's loop
        if self._is_main:
            main_loop = asyncio.get_event_loop()
            self._task = main_loop.create_task(self._run_profiling())
        else:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            self._task = loop.create_task(self._run_profiling())

            # Run loop in background thread inside child process
            def run_loop() -> None:
                loop.run_forever()

            loop_thread = Thread(target=run_loop, daemon=True)
            loop_thread.start()

    def stop(self) -> None:
        """Stop profiling and save final results."""
        logging.info(
            f"Stop profiling {'main' if self._is_main else 'child'} process. PID: {self._pid}"
        )
        if not self._running:
            return

        self._running = False

        if self._memray_enabled:
            self._flush_memray()

        if self._yappi_enabled:
            self._flush_yappi()
            yappi.stop()

        self._rotation_executor.shutdown(wait=True)

    def _get_file_name(self, profiler: str, timestamp: int, ext: str = "") -> Path:
        date = utils_general.to_iso(timestamp * 1e9)
        file_path = self._base_path / profiler / date[:10]

        if not self._is_main:
            file_path = file_path / "child" / f"proc_{self._pid}_{date[:19]}Z{ext}"
        else:
            file_path = file_path / f"main_{date[:19]}Z{ext}"

        if not os.path.exists(file_path.parent):
            file_path.parent.mkdir(parents=True, exist_ok=True)

        return file_path

    def _run_memray(self) -> None:
        """Start a new memray tracker."""
        try:
            self._memray_file = self._get_file_name(
                "memray", self._memray_next_rotation, ".bin"
            )
            self._memray_tracker = Tracker(self._memray_file, follow_fork=False)

            if self._is_main:
                # This is to allow fork process to exit Tracker,
                # as it seems when forked the tracker reference from main is still running
                # in forked process and cause an error when trying to initialized a new tracker in child
                global _tracker
                _tracker = self._memray_tracker
            self._memray_tracker.__enter__()
        except Exception as ex:
            logging.exception(f"failed to start memray tracker: {ex}")

    def _flush_memray(self) -> None:
        """Stop and flush memray tracker output"""
        try:
            if not self._is_main:
                # We check if main process tracker exist and exit before starting new Tracker in child process
                global _tracker
                try:
                    if _tracker is not None:
                        _tracker.__exit__(None, None, None)
                        _tracker = None
                except Exception as ex:
                    logging.exception(f"Error while cleaning up global tracker: {ex}")

            if self._memray_tracker and self._memray_file:
                self._memray_tracker.__exit__(None, None, None)

                # First rotate the main process file
                self._rotation_executor.submit(self._rotate_file, self._memray_file)

                self._memray_tracker = None
                self._memray_file = None

        except Exception as ex:
            logging.exception(f"failed to flush memray output: {ex}")

    def _run_yappi(self) -> None:
        """Save current yappi profile."""
        try:
            if yappi.is_running():
                return

            yappi.set_clock_type(_PROFILER_CONFIG.yappi.clock_type)
            if _PROFILER_CONFIG.yappi.profile_threads:
                yappi.set_context_id_callback(lambda: threading.current_thread().ident)
            yappi.start()

        except Exception as ex:
            logging.exception(f"failed to run yappi: {ex}")

    def _flush_yappi(self) -> None:
        """Save current yappi profile."""
        try:
            if yappi.is_running():
                base_path = self._get_file_name("yappi", self._yappi_next_rotation)
                yappi_file_func = base_path.parent / f"func_{base_path.name}.prof"
                yappi.get_func_stats().save(yappi_file_func, type="pstat")
                self._rotation_executor.submit(self._rotate_file, yappi_file_func)

                if _PROFILER_CONFIG.yappi.profile_threads:
                    yappi_file_thread = (
                        base_path.parent / f"thread_{base_path.name}.pkl"
                    )
                    with open(yappi_file_thread, "wb") as f:
                        pickle.dump(yappi.get_thread_stats(), f)
                    self._rotation_executor.submit(self._rotate_file, yappi_file_thread)

                mem_kb = yappi.get_mem_usage()
                logging.info(
                    f"Memory used by yappi profiler in {'main' if self._is_main else 'child'} process. PID: {self._pid}: {f'{mem_kb / 1024:.2f} MB' if mem_kb >= 1024 else f'{mem_kb:.2f} KB'}"
                )
                yappi.clear_stats()
        except Exception as ex:
            logging.exception(f"failed to save yappi output: {ex}")

    async def _run_profiling(self) -> None:
        """Main loop for file rotation."""

        self._memray_next_rotation = self._yappi_next_rotation = int(time.time())

        while self._running:
            try:
                current_time = time.time()

                if self._memray_enabled and current_time >= self._memray_next_rotation:
                    self._flush_memray()
                    self._run_memray()
                    self._memray_next_rotation = _calculate_next_rotation(
                        current_time, _PROFILER_CONFIG.memray.interval_sec
                    )

                if self._yappi_enabled and current_time >= self._yappi_next_rotation:
                    self._flush_yappi()
                    self._run_yappi()
                    self._yappi_next_rotation = _calculate_next_rotation(
                        current_time, _PROFILER_CONFIG.yappi.interval_sec
                    )

                next_rotation = self._get_next_rotation()
                sleep_time = next_rotation - current_time

                logging.info(
                    f"Profiler iteration finish for {'main' if self._is_main else 'child'} process. PID: {self._pid}. Next iterations at: {utils_general.to_iso(next_rotation*1e9)[:19]}Z"
                )
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                else:
                    await asyncio.sleep(1)

            except Exception as ex:
                logging.exception(f"failed to run schedule profiler rotation: {ex}")
                time.sleep(5)

    def _get_next_rotation(self) -> int:
        next_rotations = []
        if self._memray_enabled:
            next_rotations.append(self._memray_next_rotation)
        if self._yappi_enabled:
            next_rotations.append(self._yappi_next_rotation)
        return min(next_rotations)

    def _rotate_file(self, file_path: Path) -> None:
        """Compress and upload profile file."""
        if not os.path.exists(file_path):
            logging.error(
                f"Failed rotating file for PID '{self._pid}', filed not found: {file_path}"
            )
            return

        try:
            if IS_ECS:
                assert self._s3_client is not None

                gz_file_path = file_path.with_name(file_path.name + ".gz")
                with (
                    open(file_path, "rb") as f_in,
                    gzip.open(gz_file_path, "wb") as f_out,
                ):
                    shutil.copyfileobj(f_in, f_out)

                assert _PROFILER_CONFIG.s3

                # Upload to S3
                try:
                    if self._is_main:
                        prefix = f"{_PROFILER_CONFIG.s3.s3_prefix}/{file_path.parent}/{self._task_id}"
                    else:
                        prefix = f"{_PROFILER_CONFIG.s3.s3_prefix}/{file_path.parent.parent}/{self._task_id}/child"

                    s3_obj_key = f"{prefix}/{gz_file_path.name}"

                    self._s3_client.upload_file(
                        str(gz_file_path), _PROFILER_CONFIG.s3.s3_bucket, s3_obj_key
                    )

                    os.remove(gz_file_path)
                    os.remove(file_path)
                    logging.info(
                        f"Profile file successfully compressed, uploaded to S3 and removed. S3 destination: s3://{s3_obj_key}"
                    )
                except Exception as upload_ex:
                    logging.error(f"Failed to upload to S3: {upload_ex}")
                    # Keep the files locally if S3 upload fails
                    return
            else:
                logging.info(f"Running locally, file store at: {file_path}")
        except Exception as ex:
            logging.error(f"Error rotating file for process {self._pid}: {ex}")


class ProfilerManager:
    _instance: Optional["ProfilerManager"] = None
    _lock: Lock = Lock()

    def __new__(cls) -> "ProfilerManager":
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self) -> None:
        """Initialize the profiler manager with S3 client and task information."""
        with self._lock:
            self._s3_client = boto3.client("s3")
            self._task_id: Optional[str] = None
            self._running = False
            self._process_counter = Value(c_int, 0)
            self._process_profilers: Dict[int, ProcessProfiler] = {}

    def _register_process_handlers(self) -> None:
        """Register handlers for process creation and exit."""

        def on_process_start(process: BaseProcess) -> None:
            if self._running and _PROFILER_CONFIG.enable:
                assert process.pid
                self.start_process_profiling(process.pid, False)

        def on_process_exit(process: BaseProcess) -> None:
            if process.pid in self._process_profilers:
                self.stop_process_profiling(process.pid, False)

        register_after_fork(self, lambda _: on_process_start(current_process()))
        atexit.register(lambda: on_process_exit(current_process()))

    def start_process_profiling(self, pid: int, is_main: bool) -> None:
        """Start profiling a specific process."""
        if pid not in self._process_profilers:
            if not is_main:
                with self._process_counter.get_lock():
                    if (
                        not _PROFILER_CONFIG.profile_child_process
                        or self._process_counter.value  # type: ignore[attr-defined]
                        >= _PROFILER_CONFIG.child_process_count
                    ):
                        logging.info(f"Avoid profiling child process: {pid}")
                        return
                    self._process_counter.value += 1  # type: ignore[attr-defined]

            profiler = ProcessProfiler(
                pid, Path(_PROFILER_CONFIG.path), self._task_id, is_main
            )
            self._process_profilers[pid] = profiler
            profiler.start()

    def stop_process_profiling(self, pid: int, is_main: bool) -> None:
        """Stop profiling a specific process."""
        if pid in self._process_profilers:
            if not is_main:
                with self._process_counter.get_lock():
                    self._process_counter.value -= 1  # type: ignore[attr-defined]

            profiler = self._process_profilers.pop(pid)
            profiler.stop()

    def __enter__(self) -> Self:
        """Start the profilers when entering the context."""
        self._running = True

        # Start profilers in background tasks
        if _PROFILER_CONFIG.enable:
            if IS_ECS:
                loop = asyncio.get_event_loop()
                ecs_task_info = loop.run_until_complete(
                    EcsClusterManager().get_task_info()
                )
                self._task_id = ecs_task_info.get("current_task_id", "")

            # Register process start/exit handlers
            self._register_process_handlers()

            # Start profiling main process
            self.start_process_profiling(os.getpid(), True)

        return self

    def __exit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_val: Optional[BaseException],
        exc_tb: Optional[TracebackType],
    ) -> None:
        """Stop the profilers and ensure final profiles are saved."""
        self._running = False

        if _PROFILER_CONFIG.enable:
            # Stop profiling all processes
            pids = list(self._process_profilers.keys())
            for pid in pids:
                self.stop_process_profiling(pid, True)
