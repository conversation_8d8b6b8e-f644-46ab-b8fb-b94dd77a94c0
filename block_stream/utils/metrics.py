import abc
import asyncio
import json
import logging
import random
import time
from collections import defaultdict
from collections.abc import Callable
from dataclasses import dataclass
from typing import (
    Any,
    TypedDict,
)

import botocore
import utils_general
from aiobotocore.session import AioSession
from types_aiobotocore_cloudwatch import CloudWatchClient
from types_aiobotocore_cloudwatch.type_defs import (
    DimensionTypeDef,
    MetricDatumTypeDef,
    StatisticSetTypeDef,
)

from block_stream.config import IS_ECS
from block_stream.typings import TIME_PRECISION


@dataclass
class StatisticValues:
    sample_count: int = 0
    sum: float = 0.0
    minimum: float | None = None
    maximum: float | None = None


METRIC_EVENT_T = dict[tuple[str, ...], StatisticValues]


class MetricResult(TypedDict, total=False):
    # Internal format for metric data, used by MetricWorker

    dimensions: dict[str, str]  # key-value pairs stored as Cloudwatch Dimensions
    value: float
    statistic_values: StatisticSetTypeDef


class Metric(abc.ABC):
    @abc.abstractmethod
    def flush(self) -> list[MetricResult]:
        pass


class MultiMetric(Metric):
    """
    Abstract class to handle a bulk of metrics of the same type.
    """

    def __init__(
        self,
        fields: list[str],
        allowed_fields_values: dict[str, list[Any]] | None = None,
    ):
        # Metric event cache [dimension1 val, ...] -> [event1, ...]
        self._metric_events: METRIC_EVENT_T = defaultdict(StatisticValues)

        # List of dimension keys [dimension1 key, dimension2 key, ...]
        self._fields: list[str] = fields

        # Optional dict of allowed values by field
        self._allowed_field_values = allowed_fields_values

    def _reset(self) -> None:
        self._metric_events = defaultdict(StatisticValues)

    def _record_event(self, value: float, **kwargs: str) -> None:
        """
        Record a new event, such as a receipt of a streamed message.

        :param value: float, actual representation depends on implementer class
        :return: N/A
        """

        try:
            if self._allowed_field_values and not self._is_metric_allowed(**kwargs):
                return

            # Using Tuple as Dict keys must be hashable
            d_key = tuple(kwargs[field] for field in self._fields)
            self._update_statistics(self._metric_events[d_key], value)
        except Exception as e:
            logging.error(f"Error adding metric: {e}")

    def _is_metric_allowed(self, **kwargs: str) -> bool:
        if not self._allowed_field_values:
            return True

        # if key is not in _allowed_field_values, all values are allowed
        for k, v in kwargs.items():
            if (
                k in self._allowed_field_values
                and v not in self._allowed_field_values[k]
            ):
                return False
        return True

    def _update_statistics(self, stats: StatisticValues, value: float) -> None:
        stats.sample_count += 1
        stats.sum += value
        stats.minimum = min(stats.minimum, value) if stats.minimum else value
        stats.maximum = max(stats.maximum, value) if stats.maximum else value

    @abc.abstractmethod
    def _flush(self, metric_events: METRIC_EVENT_T) -> list[MetricResult]:
        pass

    def flush(self) -> list[MetricResult]:
        """
        Called by the MetricWorker to aggregate and log metrics in json format.

        :return: Dict of metrics
        """

        # Does not need to be perfect, otherwise Locks would be required
        # avoids iterating on a changing dict
        metrics = self._metric_events.copy()
        self._reset()
        return self._flush(metrics)

    def _convert_statistic_values(self, stats: StatisticValues) -> StatisticSetTypeDef:
        return {
            "SampleCount": stats.sample_count,
            "Sum": stats.sum,
            "Minimum": stats.minimum if stats.minimum else 0.0,
            "Maximum": stats.maximum if stats.maximum else 0.0,
        }


class MultiLagMetric(MultiMetric):
    """
    Metric to measure the lag between current time and some other point in time
    E.g.: lag of received data
    """

    def __init__(
        self,
        fields: list[str],
        allowed_fields_values: dict[str, list[Any]] | None = None,
        precision: TIME_PRECISION = "s",
    ):
        super().__init__(fields, allowed_fields_values)

        if precision == "s":
            ns_conversion = 1e9
        elif precision == "ms":
            ns_conversion = 1e6
        elif precision == "us":
            ns_conversion = 1e3
        elif precision == "ns":
            ns_conversion = 1
        else:
            raise ValueError(f"Invalid precision '{precision}'")
        self._ns_conversion = ns_conversion

    def add(self, t_ns: float, t_now_ns: float | None = None, **kwargs: str) -> None:
        """
        Record an event's lag, with support for multiple dimensions
        (separate metric across exchange for example)

        :param t_ns: Timestamp of event (nanoseconds)
        :param t_now_ns: Optional current time. If not set, machine time is used
        :param kwargs: Key-value pairs to set metric dimensions (e.g.: exchange, currency, etc.)
        :return: None
        """

        if t_now_ns:
            lag_time = (t_now_ns - t_ns) / self._ns_conversion
        else:
            lag_time = (time.time_ns() - t_ns) / self._ns_conversion

        self._record_event(lag_time, **kwargs)

    def _flush(self, metric_events: METRIC_EVENT_T) -> list[MetricResult]:
        results: list[MetricResult] = []
        for k_vals, stats in metric_events.items():
            keys = dict(zip(self._fields, k_vals, strict=False))

            results.append(
                {
                    "dimensions": keys,
                    "statistic_values": self._convert_statistic_values(stats),
                }
            )

        return results


class MultiRateMetric(MultiMetric):
    """
    Metric to measure rate of an event
    """

    def __init__(self, fields: list[str]):
        super().__init__(fields)
        self._last_flush = time.time()

    def add(self, **kwargs: str) -> None:
        self._record_event(1, **kwargs)

    def _flush(self, metric_events: METRIC_EVENT_T) -> list[MetricResult]:
        # Flushing may not be done on a super accurate frequency, this
        # should help calculate the per-second rate more accurately
        now = time.time()
        flush_delta = now - self._last_flush
        self._last_flush = now

        results: list[MetricResult] = []
        for k_vals, stats in metric_events.items():
            keys = dict(zip(self._fields, k_vals, strict=False))
            results.append(
                {"dimensions": keys, "value": stats.sample_count / flush_delta}
            )

        return results


class MultiNumberMetric(MultiMetric):
    def add(self, num: int | float, **kwargs: str) -> None:
        """
        Records a given number over time.
        e.g. Recording amount of running functions in a communicative stream

        :param num: Number to record
        :return: None
        """
        self._record_event(num, **kwargs)

    def _flush(self, metric_events: METRIC_EVENT_T) -> list[MetricResult]:
        results: list[MetricResult] = []
        for k_vals, stats in metric_events.items():
            keys = dict(zip(self._fields, k_vals, strict=False))
            results.append(
                {
                    "dimensions": keys,
                    "statistic_values": self._convert_statistic_values(stats),
                }
            )

        return results


class MultiCallableMetric(Metric):
    """
    Metric class that accepts a single callable to produce metrics across multiple dimensions.

    Note: The callable that is provided should return dimensions values in the same order as the fields
    otherwise, this will lead to the incorrect dimensions being created.
    """

    def __init__(
        self,
        fields: list[str],
        callable_: Callable[[], dict[tuple[str, ...], float]],
        allowed_fields_values: dict[str, list[Any]] | None = None,
    ):
        self._fields = fields
        self._callable = callable_
        self._allowed_field_values = allowed_fields_values

    def flush(self) -> list[MetricResult]:
        results: list[MetricResult] = []

        try:
            metrics = self._callable()
        except Exception as e:
            logging.exception(f"Error fetching metrics: {e}")
            return results

        for dimension_values, value in metrics.items():
            if not isinstance(value, int | float):
                logging.warning(
                    f"Metric value for dimensions {dimension_values} is non-numeric: {value}"
                )
                continue

            keys = dict(zip(self._fields, dimension_values, strict=False))
            if self._allowed_field_values and not self._is_metric_allowed(**keys):
                continue

            results.append(
                {
                    "dimensions": keys,
                    "value": value,
                }
            )

        return results

    def _is_metric_allowed(self, **kwargs: str) -> bool:
        if not self._allowed_field_values:
            return True

        for k, v in kwargs.items():
            if (
                k in self._allowed_field_values
                and v not in self._allowed_field_values[k]
            ):
                return False
        return True


class RateMetric(Metric):
    def __init__(self) -> None:
        self._last_flush = time.time()
        self._count = 0

    def add(self, increment: int = 1) -> None:
        self._count += increment

    def flush(self) -> list[MetricResult]:
        # Flushing may not be done on a super accurate frequency, this
        # should help calculate the per-second rate more accurately
        now = time.time()
        count_, self._count = self._count, 0

        flush_delta = now - self._last_flush
        self._last_flush = now

        return [{"dimensions": {}, "value": count_ / flush_delta}]


class CallableMetric(Metric):
    """
    Simple wrapper to add a callable as a Metric for easier custom metrics
    """

    def __init__(self, callable_: Callable[[], Any]):
        self._callable = callable_

    def flush(self) -> list[MetricResult]:
        try:
            value = self._callable()
        except Exception as e:
            logging.exception(f"Error fetching metric: {e}")
            return []

        return [{"dimensions": {}, "value": value}]


class MetricWorker:
    """
    Async worker to periodically log metrics in json format
    """

    def __init__(
        self,
        store_frequency_s: int,
        metrics: dict[str, Metric],
        component_name: str,
        namespace: str = "Streaming",
        cloudwatch_enabled: bool = True,
        log_full_metrics: bool = False,
    ):
        self._metrics = metrics
        self._store_frequency_s = store_frequency_s
        self._namespace = namespace
        self._component_name = component_name
        self._session = AioSession()
        self._cloudwatch_enabled = cloudwatch_enabled
        self._log_full_metrics = log_full_metrics

    def _get_metrics_cloudwatch(self) -> list[MetricDatumTypeDef]:
        """
        Retrieve all metric data and construct 'flattened' format suitable for Cloudwatch

        E.g.: of Cloudwatch format:
        ```
        {
            "MetricName": "SomeLag",
            "Dimensions": [
                {"Name": "Component", "Value": "volSmileCalc"},
            ],
            "Value":      10.23,
        }
        ```

        :return: List[cloudwatch_metric_data]
        """

        metric_data: list[MetricDatumTypeDef] = []
        for metric_name, metric in self._metrics.items():
            try:
                metric_results = metric.flush()
            except Exception as e:
                logging.exception(f"Error flushing metric {metric_name}: {e}")
                continue

            for metric_result in metric_results:
                metric_result["dimensions"]["Component"] = self._component_name

                dimensions: list[DimensionTypeDef] = [
                    {"Name": k, "Value": v}
                    for k, v in metric_result["dimensions"].items()
                ]

                data_point: MetricDatumTypeDef = {
                    "MetricName": metric_name,
                    "Dimensions": dimensions,
                }

                if "statistic_values" in metric_result:
                    data_point["StatisticValues"] = metric_result["statistic_values"]

                elif "value" in metric_result:
                    # Note: Value and StatisticValues are mutually exclusive in CloudWatch
                    data_point["Value"] = metric_result["value"]

                else:
                    raise RuntimeError(
                        "A MetricResult may only contain Value or StatisticValue"
                    )

                metric_data.append(data_point)

        return metric_data

    async def _store_metrics_cloudwatch(
        self, cloudwatch_client: CloudWatchClient, metric_data: list[MetricDatumTypeDef]
    ) -> None:
        try:
            # Expected condition on slow startup
            if len(metric_data) == 0:
                logging.warning("No metric data to store")
                return

            await cloudwatch_client.put_metric_data(
                Namespace=self._namespace, MetricData=metric_data
            )

        except botocore.exceptions.ClientError as err:
            if err.response["Error"]["Code"] == "AccessDenied":
                logging.warning(
                    "Missing permission to store metrics. Switching to log only mode"
                )
                self._cloudwatch_enabled = False
            else:
                raise err

        except Exception as e:
            logging.exception(f"Unable to store metrics: {e}")

        self._log_metrics(metric_data)

    def _log_metrics(self, metric_data: list[MetricDatumTypeDef]) -> None:
        if not self._log_full_metrics:
            logging.info(
                "Metrics flushed for %s in %s", self._component_name, self._namespace
            )
            return

        metrics_log_msg = json.dumps(metric_data, indent=2)
        logging.info(
            "Metrics flushed for %s in %s:\n%s",
            self._component_name,
            self._namespace,
            metrics_log_msg,
        )

    async def run(self) -> None:
        """
        Periodically log metrics for Cloudwatch consumption.

        Should be run as a worker async-thread. At top level:
        ```
        await asyncio.gather(
            metric_worker.run(),
            ...
        )
        ```

        :return: N/A
        """

        next_flush = int(
            time.time() // self._store_frequency_s * self._store_frequency_s
            + self._store_frequency_s
        )

        # Checking this here instead of __init__() to avoid unnecessary log spew
        if not IS_ECS and self._cloudwatch_enabled:
            # This is intended to catch overprivileged local runs
            self._cloudwatch_enabled = False
            logging.warning(
                "Task NOT running in ECS. Switching Metrics to log only mode"
            )

        async with self._session.create_client("cloudwatch") as cloudwatch_client:
            while True:
                try:
                    metric_data = self._get_metrics_cloudwatch()

                    if self._cloudwatch_enabled:
                        await self._store_metrics_cloudwatch(
                            cloudwatch_client, metric_data
                        )
                    else:
                        self._log_metrics(metric_data)

                    next_flush += self._store_frequency_s
                    flush_wait = next_flush - time.time()
                    if flush_wait > 0:
                        await asyncio.sleep(flush_wait)

                except Exception as e:
                    logging.exception(f"Unable to log metrics: {e}")


if __name__ == "__main__":
    utils_general.setup_python_logger(logging.DEBUG)

    async def run_tests() -> None:
        calculation_rate_metric = RateMetric()
        metric_worker = MetricWorker(
            10,
            {
                "calculation_per_s": calculation_rate_metric,
            },
            component_name="TestComponent",
            namespace="Stream",
            cloudwatch_enabled=True,
        )

        async def data_pusher(m: RateMetric) -> None:
            while True:
                m.add(1)
                logging.info("Added to rate metric")
                await asyncio.sleep(random.random() * 5)

        await asyncio.gather(metric_worker.run(), data_pusher(calculation_rate_metric))

    asyncio.run(run_tests())
