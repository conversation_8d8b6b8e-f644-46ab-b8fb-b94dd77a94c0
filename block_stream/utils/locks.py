import asyncio
from collections.abc import Awaitable
from types import TracebackType


class LazyInitLock:
    """
    Note: Using nest-asyncio should mean that this is no longer required.
          Consider removing it

    Wrapper for asyncio.Lock that initializes the lock on first use.

    This is useful because when the Lock is initialized, it will store a
    reference to the current event loop. If the Lock is initialized before
    an event loop is set by asyncio.run(), it will store a stale reference
    which can cause issues later.
    """

    def __init__(self) -> None:
        self._lock: asyncio.Lock | None = None

    def __aenter__(self) -> Awaitable[None]:
        if self._lock is None:
            self._lock = asyncio.Lock()

        return self._lock.__aenter__()

    def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> Awaitable[None]:
        if self._lock is None:
            raise RuntimeError("Lock was not initialized")

        return self._lock.__aexit__(exc_type, exc_val, exc_tb)

    # No need to implement acquire/release as they are not used here.
