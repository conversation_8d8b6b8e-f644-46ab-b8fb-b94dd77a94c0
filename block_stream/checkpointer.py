import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime

import utils_general
from aiobotocore.session import <PERSON><PERSON><PERSON>ession
from types_aiobotocore_dynamodb import DynamoD<PERSON>lient

from block_stream.config import CHECKPOINT_TABLE_NAME

logger = logging.getLogger("checkpointer")


@dataclass(frozen=True)
class Checkpoint:
    sequence_number: str
    timestamp: datetime = field(default_factory=datetime.utcnow)


class Checkpointer:
    def __init__(
        self,
        consumer_key: str,
        endpoint_url: str | None = None,
    ):
        self._consumer_key = consumer_key
        self._endpoint_url = endpoint_url
        self._client: DynamoDBClient | None = None

    async def _get_client(self) -> DynamoDBClient:
        """
        This logic is used to lazy-init and share an aioboto3 client.
        aioboto3 unfortunately requires the use of a context manager
        but exposing that to the consumers of this library would make the
        interface unnecessarily complex

        :return: aioboto3 Client
        """

        if not self._client:
            self._client = (
                await AioSession()
                .create_client("dynamodb", endpoint_url=self._endpoint_url)
                .__aenter__()
            )

        return self._client

    def __del__(self) -> None:
        try:
            if self._client:
                asyncio.run(self._client.__aexit__(None, None, None))
        except Exception as e:
            logger.error(f"Error while Checkpointer teardown: {e}")

    async def _create_checkpoint_table(self) -> None:
        # Note: this is only for local testing
        assert self._endpoint_url is not None
        assert "localhost" in self._endpoint_url

        client = await self._get_client()
        table = await client.create_table(
            TableName=CHECKPOINT_TABLE_NAME,
            KeySchema=[{"AttributeName": "consumer_key", "KeyType": "HASH"}],
            AttributeDefinitions=[
                {"AttributeName": "consumer_key", "AttributeType": "S"}
            ],
            BillingMode="PAY_PER_REQUEST",
        )
        logger.info(f"Checkpoint table created: {table}")

    async def save_checkpoint(self, checkpoint: Checkpoint) -> None:
        """
        Saves / updates the checkpoint

        :param checkpoint: Checkpoint with new values
        :return: N/A
        """

        logger.info(f"Saving checkpoint: {checkpoint}")
        client = await self._get_client()
        await client.put_item(
            TableName=CHECKPOINT_TABLE_NAME,
            Item={
                "consumer_key": {"S": self._consumer_key},
                "sequence_number": {"S": checkpoint.sequence_number},
                "timestamp": {"S": utils_general.to_iso(checkpoint.timestamp)},
            },
        )

    async def get_last_checkpoint(self) -> Checkpoint | None:
        client = await self._get_client()
        checkpoint = await client.get_item(
            TableName=CHECKPOINT_TABLE_NAME,
            Key={
                "consumer_key": {"S": self._consumer_key},
            },
        )

        item = checkpoint.get("Item")

        if item is None:
            return None

        return Checkpoint(
            sequence_number=item["sequence_number"]["S"],
            timestamp=utils_general.from_iso(item["timestamp"]["S"]),
        )
