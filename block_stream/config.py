import os
from multiprocessing import cpu_count

CHECKPOINT_TABLE_NAME = "blockstream_checkpoint"
MAX_KINESIS_PUT_BUFFER = 500
MAX_KINESIS_FETCH_DELAY_S = 3.0
WARN_IF_BEHIND_MS = 10000
NUM_WORKERS = int(os.getenv("NUM_WORKERS", cpu_count() * 2))
MIN_KINESIS_FETCH_DELAY_S = float(os.getenv("MIN_KINESIS_FETCH_DELAY_S", 2))

# Consumer subscription management
BLOCKSTREAM_ENDPOINT = os.environ.get("BLOCKSTREAM_ENDPOINT")
ACTIVE_FUNCTION_WATCHDOG_S = 3600  # 1 hr

# Note, this should be injected by Agents over version 1.39.0
TASK_METADATA_URL = os.environ.get("ECS_CONTAINER_METADATA_URI_V4")
IS_ECS = TASK_METADATA_URL is not None

# Will override auto-scaling consumer's index for TESTING
# Format should be "index/total" e.g.: "0/2"
ECS_INDEX_OVERRIDE = os.environ.get("ECS_INDEX_OVERRIDE")

# Note: this is intended to be removed and made the default option going forward
COMPRESS_OUTPUT = os.environ.get("COMPRESS_OUTPUT", "True") == "True"

# nest-asyncio breaks some tasks but is necessary to others...
DISABLE_NEST_ASYNCIO = os.environ.get("DISABLE_NEST_ASYNCIO", "False") == "True"

KINESIS_UPDATE_OUTPUT_STREAMS_SEC = 86400  # 1 day

MAX_SUBSCRIBE_RETRIES = 3
RETRY_DELAY_SECONDS = 2
