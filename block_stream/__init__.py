from block_stream.agent import Agent
from block_stream.config import DISABLE_NEST_ASYNCIO
from block_stream.consumer.consumer_subscription_manager import (
    ConsumerBaseFunctionClass,
    ConsumerSubMessage,
    ConsumerSubscriptionManager,
    Subscriber,
)
from block_stream.utils.metrics import (
    CallableMetric,
    MetricWorker,
    MultiLagMetric,
    RateMetric,
)

# Fixes weirdness around async functions:
# - Wrapping some async functions in non-async and wrapping that in async caller
#   (this applies specifically to the AutoScalingCacheManager that triggers
#    Cache loads from an async function)
# - Fixes initialising asyncio Lock() in one thread but then accessing it in another
if not DISABLE_NEST_ASYNCIO:
    import nest_asyncio  # type: ignore

    nest_asyncio.apply()


__all__ = [
    "Agent",
    "ConsumerSubMessage",
    "ConsumerSubscriptionManager",
    "ConsumerBaseFunctionClass",
    "Subscriber",
    "CallableMetric",
    "MetricWorker",
    "MultiLagMetric",
    "RateMetric",
]
