import asyncio
import copy
import gzip
import json
import logging
import time
from collections.abc import AsyncIterator, Iterable
from datetime import UTC, datetime
from typing import Any

from block_stream.channel import Channel
from block_stream.typings import REPLAY_MODE


class Replayer(Channel):
    """
    Wraps a Channel to record/replay all the Incoming messages.
    This class should not be instantiated directly, instead set the
    `replay_mode` param when instantiating an Agent.
    """

    def __init__(
        self,
        channel: Channel,
        prefix: str | None,
        mode: REPLAY_MODE = "record",
    ):
        self._channel = channel
        self._mode: REPLAY_MODE = mode

        if prefix:
            prefix += "_"
        else:
            prefix = ""

        self._file_name = (
            f"recorded_{prefix}{channel.name}_{channel.shard_index}.json.gz"
        )

        self._messages: list[tuple[float, dict[str, object]]] = []

    async def put(self, record: dict[str, object], key: str = "default") -> bool:
        return await self._channel.put(record=record, key=key)

    async def put_batch(
        self,
        batch: Iterable[tuple[dict[str, Any], str]],
    ) -> None:
        await self._channel.put_batch(batch)

    async def flush(self) -> bool:
        return await self._channel.flush()

    def __aiter__(self) -> AsyncIterator[Any]:
        if self._mode == "record":
            logging.info(
                f"Recording messages for {self._channel} into {self._file_name}"
            )

            async def message_recorder() -> AsyncIterator[Any]:
                next_store_tstamp = time.time()

                async for message in self._channel:
                    self._messages.append(
                        (datetime.now(tz=UTC).timestamp(), copy.copy(message))
                    )
                    if time.time() >= next_store_tstamp:
                        self.store_replay()
                        next_store_tstamp += 60

                    yield message

            return message_recorder()

        elif self._mode == "replay":

            async def replayer() -> AsyncIterator[Any]:
                with gzip.open(self._file_name, "r") as f:
                    messages = json.load(f)["data"]

                for _, message in messages:
                    yield message

                logging.info(f"Reached the end of replay on {self._channel}")
                while True:
                    await asyncio.sleep(1.0)

            return replayer()

        elif self._mode == "replay_realtime":
            with gzip.open(self._file_name, "r") as f:
                messages = json.load(f)["data"]

            async def replayer() -> AsyncIterator[Any]:
                last_tstamp = messages[0][0]
                last_yield = datetime.now(tz=UTC).timestamp()
                for timestamp, message in messages:
                    yield message

                    wait_time = (
                        timestamp
                        - last_tstamp
                        - (datetime.now(tz=UTC).timestamp() - last_yield)
                    )
                    if wait_time > 0:
                        await asyncio.sleep(wait_time)

                    last_tstamp = timestamp
                    last_yield = datetime.now(tz=UTC).timestamp()

                logging.info(f"Reached the end of replay on {self._channel}")
                while True:
                    await asyncio.sleep(1.0)

            return replayer()

    def store_replay(self) -> None:
        if len(self._messages) == 0:
            logging.warning(f"No messages recorded, skipping save: {self._file_name}")
            return

        storage_start = time.time()
        with gzip.open(self._file_name, "w") as f:
            data_dump = json.dumps(
                {
                    "channel_name": self._channel.name,
                    "channel_shard": self._channel.shard_index,
                    "data": self._messages,
                },
            ).encode("utf-8")
            f.write(data_dump)

        store_time = time.time() - storage_start
        logging.info(
            f"Dumped recorded messages to: {self._file_name} in {store_time:.3f}s"
        )
