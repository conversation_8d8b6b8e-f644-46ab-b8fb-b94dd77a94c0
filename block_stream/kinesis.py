import asyncio
import atexit
import json
import logging
import sys
import time
from abc import ABC, abstractmethod
from collections import defaultdict
from collections.abc import AsyncIterator, Callable
from datetime import UTC, datetime, timedelta
from typing import (
    Any,
)

import backoff
import utils_general
import zstd
from aiobotocore.session import AioSession
from botocore.exceptions import Client<PERSON><PERSON><PERSON>
from types_aiobotocore_kinesis import KinesisClient
from types_aiobotocore_kinesis.literals import ShardIteratorTypeType
from types_aiobotocore_kinesis.type_defs import (
    ListStreamConsumersOutputTypeDef,
    PutRecordsOutputTypeDef,
    PutRecordsRequestEntryTypeDef,
    ShardTypeDef,
    StartingPositionTypeDef,
    StreamSummaryTypeDef,
    SubscribeToShardOutputTypeDef,
)

from block_stream.checkpointer import Checkpoint, Checkpointer
from block_stream.config import (
    COMPRESS_OUTPUT,
    ECS_INDEX_OVERRIDE,
    IS_ECS,
    KINESIS_UPDATE_OUTPUT_STREAMS_SEC,
    MAX_KINESIS_FETCH_DELAY_S,
    MAX_SUBSCRIBE_RETRIES,
    MIN_KINESIS_FETCH_DELAY_S,
    WARN_IF_BEHIND_MS,
)
from block_stream.typings import (
    CONSUMER_MODE,
    OUTPUT_MODE,
    FanoutConsumer,
)
from block_stream.utils.ecs_cluster_manager import EcsClusterManager


class StreamTransportLayer(ABC):
    @abstractmethod
    async def put_records(self, record_pack: dict[str, list[Any]]) -> None:
        pass


def _giveup_subscribe_to_shard(exc: Exception) -> bool:
    """
    Predicate used to determine whether we should give up attempting subscribing to a shard.
    """
    return isinstance(exc, ClientError) and exc.response["Error"]["Code"] in {
        "ResourceNotFoundException",
        "InvalidArgumentException",
    }


class Kinesis(StreamTransportLayer):
    def __init__(
        self,
        stream_name: str,
        app_name: str,
        shard_index: int,
        output_stream_names_filter: Callable[[str], bool] | None = None,
        endpoint_url: str | None = None,
        checkpointer: Checkpointer | None = None,
        seconds_per_checkpoint: int | None = 60,
        consumer_mode: CONSUMER_MODE = None,
        output_mode: OUTPUT_MODE = None,
        input_stream_index: int | None = None,
        enable_backup_input_stream: bool = True,
        include_sequence_number: bool = False,
    ):
        self._base_stream_name = stream_name
        self._input_stream_name = stream_name
        self._output_stream_names: list[str] = []
        self._output_stream_names_filter = (
            output_stream_names_filter
            if output_stream_names_filter
            else self._default_stream_name_filter
        )

        self._shard_index = shard_index
        self._endpoint_url = endpoint_url
        self._seconds_per_checkpoint = seconds_per_checkpoint
        self._checkpointer = checkpointer
        self._app_name = app_name
        self._session = AioSession()
        self._client: KinesisClient | None = None
        self._consumer_mode = consumer_mode

        # default to "multiple", if the stream_name is unique, this won't affect any of the current streams as
        # it won't find any additional streams
        self._output_mode = output_mode if output_mode else "multiple"

        self._input_stream_index = input_stream_index
        self._enable_backup_input_stream = enable_backup_input_stream
        self._running_tasks: set[asyncio.Task[None]] = set()
        self._include_sequence_number = include_sequence_number

        if self._endpoint_url and "localhost" in self._endpoint_url:
            # connecting to localhost kinesis, check if stream exist and create if missing
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self._create_channel(1))

    def _default_stream_name_filter(self, stream_name: str) -> bool:
        """
        Default function to filter stream_name based on base_stream_name

        :param stream_name: stream name to filter
        :return: True if stream_name starts with the base stream_name, False otherwise.
        """
        return stream_name.startswith(self._base_stream_name)

    async def _get_client(self) -> KinesisClient:
        if self._client is None:
            self._client = await self._session.create_client(
                "kinesis", endpoint_url=self._endpoint_url
            ).__aenter__()
        return self._client

    async def _create_channel(self, shard_count: int = 1) -> bool:
        """
        Check if exists, create if missing. This should most likely be disabled
        on prod

        :param shard_count: Sets shard count if channel gets created
        :return: True if newly created
        """

        try:
            logging.info(
                f"validating stream {self._input_stream_name} exist, creating if missing"
            )

            client = await self._get_client()
            current_streams = await client.list_streams()
            if self._input_stream_name in current_streams["StreamNames"]:
                return False

            await client.create_stream(
                StreamName=self._input_stream_name, ShardCount=shard_count
            )
            await client.get_waiter("stream_exists").wait(
                StreamName=self._input_stream_name
            )

            return True

        except ClientError as e:
            raise RuntimeError(
                f"Couldn't create stream: {self._input_stream_name}: {e}"
            ) from e

    async def _list_shards(self) -> list[ShardTypeDef]:
        client = await self._get_client()
        try:
            list_shards_rsp = await client.list_shards(
                StreamName=self._input_stream_name,
                ShardFilter={"Type": "AT_LATEST"},  # Exclude "closed" shards
            )
            shards = list_shards_rsp["Shards"]

        except ClientError as e:
            # Local Kinesis unfortunately doesn't support list_shards
            # this is a bit of an overarching problem with local testing
            if e.response["Error"]["Code"] == "UnknownOperationException":
                describe_stream_rsp = await client.describe_stream(
                    StreamName=self._input_stream_name
                )
                shards = describe_stream_rsp["StreamDescription"]["Shards"]
            else:
                raise e

        # Sorting shards by hash key to make indexing easier to understand
        sorted(shards, key=lambda x: x["HashKeyRange"]["StartingHashKey"])
        return shards

    async def _get_available_input_stream_summaries(self) -> list[StreamSummaryTypeDef]:
        client = await self._get_client()
        list_streams_rsp = await client.list_streams()

        now = datetime.now(UTC)
        # filter streams:
        # - status ACTIVE
        # - starts with base_stream_name
        # - stream creation should be older than KINESIS_UPDATE_OUTPUT_STREAMS_SEC
        stream_summaries = [
            stream_info
            for stream_info in list_streams_rsp["StreamSummaries"]
            if stream_info["StreamStatus"] == "ACTIVE"
            and self._default_stream_name_filter(stream_info["StreamName"])
            and (now - stream_info["StreamCreationTimestamp"])
            > timedelta(seconds=KINESIS_UPDATE_OUTPUT_STREAMS_SEC)
        ]

        return stream_summaries

    async def _update_input_stream(self) -> None:
        """
        Update the stream to read from, based on the available streams. If _input_stream_index was set, then we
        select stream based on the index from the list.
        """

        # retrieve the list of available streams
        stream_summaries = await self._get_available_input_stream_summaries()

        if not stream_summaries:
            logging.error("no valid input streams available to read from")
            return

        if self._input_stream_index is not None:
            # generate stream name based on the index
            input_stream = f"{self._base_stream_name}_{self._input_stream_index}"
            available_stream_names = [steam["StreamName"] for steam in stream_summaries]

            if input_stream not in available_stream_names:
                # allow index 0 to target base_stream_name. e.g. channel_tickData
                if (
                    self._input_stream_index != 0
                    or self._base_stream_name not in available_stream_names
                ):
                    logging.error(
                        f"Invalid input stream index: {self._input_stream_index}. "
                        f"Stream '{input_stream}' is not available or was created within the last "
                        f"{KINESIS_UPDATE_OUTPUT_STREAMS_SEC}s, so it may not include all data. "
                        f"Available streams: {available_stream_names}"
                    )
                    return

                input_stream = self._base_stream_name

        else:
            client = await self._get_client()
            fanout_consumers_count: dict[str, int] = {}
            # list streams fanout consumers
            for stream in stream_summaries:
                rsp: ListStreamConsumersOutputTypeDef = (
                    await client.list_stream_consumers(StreamARN=stream["StreamARN"])
                )
                valid_fanout_consumer = [
                    consumer
                    for consumer in rsp.get("Consumers", [])
                    if consumer["ConsumerStatus"] == "ACTIVE"
                ]
                fanout_consumers_count[stream["StreamName"]] = len(
                    valid_fanout_consumer
                )

            # choose the stream with fewer consumers order by stream name
            input_stream = min(
                fanout_consumers_count, key=lambda x: (fanout_consumers_count[x], x)
            )

        logging.info(f"setting input streams to: {input_stream}")
        self._input_stream_name = input_stream

        return

    async def update_output_stream_list(self) -> bool:
        """
        Update the output stream list. If output_mode is 'multiple', it fetches the list of available streams and
        filters them using the filter function.

        :return: True if the list was updated successfully, False otherwise
        """
        if self._output_mode == "single":
            # In single mode we output to the same input stream
            self._output_stream_names = [self._input_stream_name]
            return True

        client = await self._get_client()
        list_streams_rsp = await client.list_streams()
        output_stream_names = [
            stream_info["StreamName"]
            for stream_info in list_streams_rsp["StreamSummaries"]
            if stream_info["StreamStatus"] == "ACTIVE"
            and self._output_stream_names_filter(stream_info["StreamName"])
        ]

        if len(self._output_stream_names) != len(output_stream_names):
            logging.info(f"setting output streams list -> {output_stream_names}")

        self._output_stream_names = output_stream_names
        if not self._output_stream_names:
            logging.error("output streams list is empty")

        return True

    async def _retry_failed_put_records(
        self,
        stream_name: str,
        records: list[PutRecordsRequestEntryTypeDef],
        put_results: PutRecordsOutputTypeDef,
        retries: int,
    ) -> None:
        """
        Selectively retry putting records that failed to send

        :param stream_name: stream to output
        :param records: Full list of records in the attempted request
        :param put_results: Result of the request (indicates which records failed)
        :param retries: Remaining number of retries
        :return: N/A
        """

        retryable: list[PutRecordsRequestEntryTypeDef] = []
        seen_error_codes: dict[str, int] = defaultdict(int)

        # Find all the records that failed
        for record, result in zip(records, put_results["Records"], strict=False):
            error_code = result.get("ErrorCode")
            if error_code:
                retryable.append(record)
                seen_error_codes[error_code] += 1

        if retries:
            logging.warning(
                f"Retrying put records on '{stream_name}': {seen_error_codes}"
            )
            await self._put_records_on_stream(
                stream_name, retryable, retries=retries - 1
            )

        else:
            # Note: some of these messages can get very long, pointless to log the whole thing
            str_retryable = str(retryable)[:500]
            raise RuntimeError(
                f"Unable to put records, exceeded retries: {str_retryable}"
            )

    async def _put_records_on_stream(
        self,
        stream_name: str,
        records: list[PutRecordsRequestEntryTypeDef],
        retries: int,
    ) -> None:
        """
        Puts a set of serialized records on the Queue with retry

        :param stream_name: stream to output
        :param records: [PutRecordsRequestEntryTypeDef, ...]
        :param retries: Number of remaining retries
        :return:
        """
        client = await self._get_client()
        result = await client.put_records(
            StreamName=stream_name,
            Records=records,
        )

        if result["FailedRecordCount"] != 0:
            await self._retry_failed_put_records(
                stream_name, records, result, retries=retries
            )

        # Avoid f-strings to avoid rendering 'results' if not in debug mode
        utils_general.log_bsdebug(
            "Records (%d) were put on '%s': %s", len(records), stream_name, records
        )

    async def put_records(
        self, record_pack: dict[str, list[Any]], retries: int = 3
    ) -> None:
        """
        Puts one or more buffered records on the stream

        :param record_pack: Dict of key -> List[records for batching]
        :param retries: Maximum number of retries
        :return: N/A
        """

        # TODO: handle the case where non-json stuff is being put which will fail at decode

        serialized_records: list[PutRecordsRequestEntryTypeDef] = []

        try:

            def add_records_safe(key: str, records: list[object]) -> None:
                try:
                    serialized = json.dumps({"batch": records}).encode()
                    if COMPRESS_OUTPUT:
                        # Default compression level=3 is a good compromise
                        serialized = zstd.compress(serialized)

                    # 1MB with headroom (Kinesis max)
                    if sys.getsizeof(serialized) > 800_000:
                        logging.warning(
                            f"Attempting to batch too many records: "
                            f"{len(records)=:} ({sys.getsizeof(serialized) / 2 ** 20:.2f}MiB) "
                            f"[{key}] Splitting"
                        )
                        midpoint = len(records) // 2
                        add_records_safe(key, records[:midpoint])
                        add_records_safe(key, records[midpoint:])
                    else:
                        serialized_records.append(
                            {"PartitionKey": key, "Data": serialized}
                        )
                except Exception as e:
                    logging.exception(f"Failed to add record: {e}")
                    return

            for key, records in record_pack.items():
                add_records_safe(key, records)

            if not self._output_stream_names:
                await self.update_output_stream_list()

            tasks = []
            for stream_name in self._output_stream_names:
                tasks.append(
                    self._put_records_on_stream(
                        stream_name, serialized_records, retries=retries
                    )
                )

            await asyncio.gather(*tasks)

        except ClientError as e:
            # In case we can't find one of the output_streams, we try to update the list again.
            # This allows us to remove or disable a stream at runtime without having to restart all Calcs or
            # wait for them to update the list on their own.
            if e.response["Error"]["Code"] == "ResourceNotFoundException":
                # We validate if the output stream list has changed; if not, we raise an error,
                # as it means the list is still the same.
                current_stream_list = self._output_stream_names
                await self.update_output_stream_list()
                if current_stream_list == self._output_stream_names:
                    raise e

    async def get_shard(self) -> ShardTypeDef:
        shards = await self._list_shards()

        if self._shard_index >= len(shards):
            raise RuntimeError(f"Shard #{self._shard_index} does not exist")

        return shards[self._shard_index]

    async def _get_iterator(
        self,
        start_timestamp: datetime | None = None,
        start_sequence_number: str | None = None,
    ) -> str:
        # Note: in the non-fanout version, there is only ever one shard
        # Always get shard and client first
        shard = await self.get_shard()
        client = await self._get_client()

        # Check if checkpointing enabled
        checkpoint = None
        if self._seconds_per_checkpoint:
            if self._checkpointer is None:
                # It's important that the Checkpointer refers to a ShardId instead of an index
                self._checkpointer = Checkpointer(
                    consumer_key=f"{self._app_name}/{self._input_stream_name}/{shard['ShardId']}",
                )
            checkpoint = await self._checkpointer.get_last_checkpoint()

        sequence_number = start_sequence_number or (
            checkpoint.sequence_number if checkpoint else None
        )

        # === Use explicit starting sequence number ===
        if sequence_number:
            try:
                iterator = await client.get_shard_iterator(
                    StreamName=self._input_stream_name,
                    ShardId=shard["ShardId"],
                    ShardIteratorType="AFTER_SEQUENCE_NUMBER",
                    StartingSequenceNumber=sequence_number,
                )
            except ClientError as e:
                code = e.response.get("Error", {}).get("Code")
                if code in {"ResourceNotFoundException", "InvalidArgumentException"}:
                    logging.warning(
                        f"Invalid or expired sequence number, falling back to TRIM_HORIZON: {e}"
                    )
                    iterator = await client.get_shard_iterator(
                        StreamName=self._input_stream_name,
                        ShardId=shard["ShardId"],
                        ShardIteratorType="TRIM_HORIZON",
                    )
                else:
                    raise

        # === Use Existing checkpoint ===
        elif checkpoint:
            logging.info(f"Stream {self._input_stream_name} found {checkpoint=}")
            iterator = await client.get_shard_iterator(
                StreamName=self._input_stream_name,
                ShardId=shard["ShardId"],
                ShardIteratorType="AFTER_SEQUENCE_NUMBER",
                StartingSequenceNumber=checkpoint.sequence_number,
            )

        # === Use Start Timestamp ===
        elif start_timestamp:
            logging.info(
                f"Stream {self._input_stream_name} starting from timestamp: {start_timestamp.isoformat()}"
            )
            iterator = await client.get_shard_iterator(
                StreamName=self._input_stream_name,
                ShardId=shard["ShardId"],
                ShardIteratorType="AT_TIMESTAMP",
                Timestamp=start_timestamp,
            )

        # === Use LATEST  ===
        else:
            # If checkpointing is enabled (but no checkpoint yet) start from beginning
            # otherwise (checkpointing disabled) just consume from the end
            iterator_type: ShardIteratorTypeType = (
                "TRIM_HORIZON" if self._seconds_per_checkpoint else "LATEST"
            )
            logging.info(
                f"Iterating {self._input_stream_name}/{shard['ShardId']} from {iterator_type}"
            )

            # NOTE: shard_iterator expires after 5 mins (might need refresh)
            iterator = await client.get_shard_iterator(
                StreamName=self._input_stream_name,
                ShardId=shard["ShardId"],
                ShardIteratorType=iterator_type,
            )

        return iterator["ShardIterator"]

    def _create_clean_dangling_consumer_tasks(self) -> None:
        # TODO: really this should be awaited but for now just reference to silence the linter warning.
        # NOTE: Some components don't shut down fast enough, hence repeating the check
        for delay in [60, 300, 900, 1800]:
            task = asyncio.create_task(
                self._clean_dangling_fanout_consumers_delay(delay)
            )
            self._running_tasks.add(task)
            task.add_done_callback(lambda t: self._running_tasks.remove(t))

    def _add_sequence_number_data(
        self,
        data: dict[str, Any],
        sequence_number: str | None,
        approx_timestamp: datetime | None,
    ) -> dict[str, Any]:
        """Adds sequence number to the data if required."""
        if sequence_number is None:
            logging.error(
                "SequenceNumber Not found from kinesis, cannot attach to data."
            )
        if self._include_sequence_number:
            if sequence_number:
                data["sequence_number"] = sequence_number
            if approx_timestamp:
                data["approx_timestamp"] = approx_timestamp

        return data

    async def _iterate_records_unregistered(
        self,
        start_timestamp: datetime | None,
        start_sequence_number: str | None = None,
    ) -> AsyncIterator[Any]:
        await self._update_input_stream()

        self._create_clean_dangling_consumer_tasks()
        logging.info(
            f"Starting to iterate UNREGISTERED consumer on stream: {self._input_stream_name}"
        )

        iterator = await self._get_iterator(
            start_timestamp=start_timestamp,
            start_sequence_number=start_sequence_number,
        )

        if self._seconds_per_checkpoint:
            next_checkpoint = time.time() + self._seconds_per_checkpoint
        else:
            next_checkpoint = None

        # Gradual backoff and retry
        fetch_delay_s = MIN_KINESIS_FETCH_DELAY_S
        client = await self._get_client()

        while True:
            t_start = time.time()
            try:
                response = await client.get_records(ShardIterator=iterator)
            except ClientError as e:
                code = e.response["Error"]["Code"]
                if code == "ProvisionedThroughputExceededException":
                    fetch_delay_s = min(fetch_delay_s * 2, MAX_KINESIS_FETCH_DELAY_S)
                    await asyncio.sleep(fetch_delay_s)
                    continue
                elif code == "ExpiredIteratorException":
                    # Unlikely event but an Iterator may expire in 5 minutes in which case
                    # it needs to be refreshed. This should be tested thoroughly to avoid
                    # re-calculating records as much as possible
                    iterator = await self._get_iterator(
                        start_timestamp=start_timestamp,
                        start_sequence_number=start_sequence_number,
                    )
                    continue
                elif code == "ExpiredTokenException":
                    logging.warning("Expired token. Retrying after 1s.")
                    await asyncio.sleep(1)
                    response = await client.get_records(ShardIterator=iterator)
                else:
                    raise e

            for record in response["Records"]:
                try:
                    # No obvious way to tell compressed data, attempt to decompress
                    # as we intend to default to compression soon anyway
                    try:
                        record_data = zstd.decompress(record["Data"])
                    except zstd.Error:
                        record_data = record["Data"]

                    decoded = utils_general.json_loads(record_data)
                    sequence_number = record.get("SequenceNumber")
                    approx_timestamp = record.get("ApproximateArrivalTimestamp")
                    if "data" in decoded:
                        yield self._add_sequence_number_data(
                            decoded["data"], sequence_number, approx_timestamp
                        )
                    elif "batch" in decoded:
                        for row in decoded["batch"]:
                            yield self._add_sequence_number_data(
                                row, sequence_number, approx_timestamp
                            )
                    else:
                        yield self._add_sequence_number_data(
                            decoded, sequence_number, approx_timestamp
                        )
                except Exception as e:
                    logging.exception(f"Failed to handle record: {record} {e}")
            iterator = response["NextShardIterator"]
            t_end = time.time()
            if (
                self._seconds_per_checkpoint
                and next_checkpoint
                and next_checkpoint <= t_end
                and response["Records"]
            ):
                assert self._checkpointer is not None
                await self._checkpointer.save_checkpoint(
                    Checkpoint(response["Records"][-1]["SequenceNumber"])
                )
                next_checkpoint += self._seconds_per_checkpoint

            if response["MillisBehindLatest"] > WARN_IF_BEHIND_MS:
                msg = f"{self._input_stream_name}/{self._shard_index} behind by {response['MillisBehindLatest'] / 1000:.1f}s"
                logging.warning(msg)

            # Gradual back-off (up to limit) if no records received
            if not response["Records"]:
                fetch_delay_s = min(fetch_delay_s * 1.25, MAX_KINESIS_FETCH_DELAY_S)
            else:
                fetch_delay_s = MIN_KINESIS_FETCH_DELAY_S

            # Finally wait before getting records
            remaining_time = fetch_delay_s - (t_end - t_start)
            if remaining_time > 0:
                await asyncio.sleep(remaining_time)

    async def _register_consumer(
        self,
        stream_arn: str,
        consumer_name: str,
        retries: int = 5,
    ) -> str:
        client = await self._get_client()
        try:
            register_consumer_response = await client.register_stream_consumer(
                StreamARN=stream_arn,
                ConsumerName=consumer_name,
            )
            consumer_arn = register_consumer_response["Consumer"]["ConsumerARN"]
            logging.info(f"Successfully registered new consumer: {consumer_arn}")
            return consumer_arn

        except ClientError as e:
            if e.response["Error"]["Code"] == "LimitExceededException":
                if retries == 0:
                    if (
                        self._input_stream_index is not None
                        and self._enable_backup_input_stream
                    ):
                        logging.error(
                            f"Fanout consumer limit exceeded for stream: {self._input_stream_name} "
                            f"using index '{self._input_stream_index}'. Enabling backup streams and retrying."
                        )
                        # Set index to None so we are able to select the stream based on number of fanout consumers
                        self._input_stream_index = None
                        await self._update_input_stream()
                        describe_stream_response = await client.describe_stream(
                            StreamName=self._input_stream_name
                        )
                        stream_arn = describe_stream_response["StreamDescription"][
                            "StreamARN"
                        ]
                        return await self._register_consumer(stream_arn, consumer_name)
                    raise RuntimeError(f"Retries exceeded: {e}") from e

                # Max 5 registrations in progress at a time. Wait and retry
                logging.warning("Unable to register consumer: LimitExceeded. Retrying")
                await asyncio.sleep(5)
                if self._input_stream_index is None:
                    # update input stream in case number of fanout consumers changed
                    await self._update_input_stream()
                return await self._register_consumer(
                    stream_arn, consumer_name, retries - 1
                )

            elif e.response["Error"]["Code"] == "ResourceInUseException":
                describe_consumer_response = await client.describe_stream_consumer(
                    StreamARN=stream_arn,
                    ConsumerName=consumer_name,
                )
                consumer_arn = describe_consumer_response["ConsumerDescription"][
                    "ConsumerARN"
                ]
                logging.info(f"Found an existing consumer, reusing: {consumer_arn}")
                return consumer_arn

            else:
                raise e

    async def _clean_dangling_fanout_consumers(
        self, family_name: str, running_consumer_ids: list[str]
    ) -> None:
        try:
            client = await self._get_client()

            # retrieve the list of available streams
            available_streams = await self._get_available_input_stream_summaries()

            for stream_details in available_streams:
                input_stream = stream_details["StreamName"]
                logging.info(f"Checking for dangling consumers in: {input_stream}")

                stream_arn = stream_details["StreamARN"]

                # Note: this will only retrieve at most 100 consumers, however,
                #       given the Kinesis limits, this should not be causing any issues
                list_consumers_response = await client.list_stream_consumers(
                    StreamARN=stream_arn,
                )

                dangling_consumers = []
                for consumer_description in list_consumers_response.get(
                    "Consumers", []
                ):
                    if consumer_description["ConsumerStatus"] != "ACTIVE":
                        continue

                    # E.g.: volSmileFlexStream-task-definition.08ff47d779d547f9a78f23671bb300dd
                    #       where the first part is the ECS Task Family
                    #       the second one is the ECS Task ID
                    consumer_name_split = consumer_description["ConsumerName"].split(
                        "."
                    )

                    if consumer_name_split[0] != family_name:
                        continue

                    if consumer_name_split[-1] not in running_consumer_ids:
                        dangling_consumers.append(consumer_description["ConsumerName"])

                if not dangling_consumers:
                    logging.info(f"No dangling consumers found in {input_stream}")
                    continue

                for consumer_name in dangling_consumers:
                    deregister_consumer_response = (
                        await client.deregister_stream_consumer(
                            StreamARN=stream_arn, ConsumerName=consumer_name
                        )
                    )
                    logging.info(
                        f"Deregistering consumer {consumer_name} from {input_stream}: {deregister_consumer_response}"
                    )

        except Exception as e:
            logging.exception(f"Unable to clean dangling consumers: {e}")

    async def _clean_dangling_fanout_consumers_delay(
        self, delay_seconds: int = 0
    ) -> None:
        """
        There's a race on ECS deployment that may result in the old tasks still running
        while the new tasks have already started. This can prevent the dangling consumer
        check on startup from succeeding, hence the added delay
        """

        try:
            if delay_seconds:
                await asyncio.sleep(delay_seconds)

            logging.info(f"Running dangling consumer check (after {delay_seconds=})")

            cluster_manager = EcsClusterManager()
            task_info = await cluster_manager.get_task_info()

            family_name = task_info.get("family_name") or ""
            running_consumer_ids = task_info.get("running_task_ids") or []

            await self._clean_dangling_fanout_consumers(
                family_name=family_name,
                running_consumer_ids=running_consumer_ids,
            )

        except Exception as e:
            logging.exception(f"Unable to clean dangling consumers: {e}")

    async def _get_active_consumer_arn(self, consumer_name: str) -> str:
        client = await self._get_client()

        def deregister_consumer_fn() -> None:
            # Synchronous deregister function due to potential
            # problems trying to pull an async on atexit()
            try:
                response = client.deregister_stream_consumer(
                    StreamARN=stream_arn, ConsumerName=consumer_name
                )
                logging.info(f"Deregistering consumer {consumer_name}: {response}")

            except Exception as e:
                logging.exception(f"Unable to deregister: {e}")

        # Registering the consumer
        describe_stream_response = await client.describe_stream(
            StreamName=self._input_stream_name
        )
        stream_arn = describe_stream_response["StreamDescription"]["StreamARN"]
        consumer_arn = await self._register_consumer(stream_arn, consumer_name)

        # Waiting for consumer to become active
        for _ in range(10):
            describe_consumer_response = await client.describe_stream_consumer(
                StreamARN=stream_arn,
                ConsumerARN=consumer_arn,
            )

            consumer_status = describe_consumer_response["ConsumerDescription"][
                "ConsumerStatus"
            ]
            if consumer_status == "ACTIVE":
                atexit.register(deregister_consumer_fn)
                logging.info(f"Active consumer ready: {consumer_arn}")
                return consumer_arn

            elif consumer_status == "CREATING":
                logging.info("Creating consumer... Waiting for ACTIVE status")
                await asyncio.sleep(5)
            else:
                # Error state: DELETING, but formulating this as a catch-all
                raise RuntimeError(
                    f"Consumer status: {consumer_status} {consumer_arn}, but it should be CREATING/ACTIVE"
                )

        raise RuntimeError(
            f"Consumer did not reach ACTIVE status before timeout: {consumer_arn}"
        )

    async def _initialise_fanout_consumer(self) -> FanoutConsumer:
        """
        Performs one-time garbage collection and initialises a new consumer
        """

        cluster_manager = EcsClusterManager()
        task_info = await cluster_manager.get_task_info()
        instance_id = task_info["current_task_id"]
        consumer_name = f"{task_info['family_name']}.{instance_id}"
        consumer_arn = await self._get_active_consumer_arn(consumer_name)

        return {"arn": consumer_arn, "name": consumer_name}

    @backoff.on_exception(
        backoff.expo,
        ClientError,
        max_tries=MAX_SUBSCRIBE_RETRIES,
        giveup=_giveup_subscribe_to_shard,
    )
    async def _subscribe_to_client_shard_with_backoff(
        self,
        consumer_arn: str,
        shard_id: str,
        starting_position: StartingPositionTypeDef,
    ) -> SubscribeToShardOutputTypeDef:
        client = await self._get_client()
        return await client.subscribe_to_shard(
            ConsumerARN=consumer_arn,
            ShardId=shard_id,
            StartingPosition=starting_position,
        )

    async def _iterate_records_fanout(
        self,
        starting_timestamp: datetime | None = None,
        start_sequence_number: str | None = None,
    ) -> AsyncIterator[Any]:
        await self._update_input_stream()
        consumer = await self._initialise_fanout_consumer()

        # tasks to clean dangling consumers
        self._create_clean_dangling_consumer_tasks()
        logging.info(
            f"Starting to iterate FANOUT consumer on: {self._input_stream_name} as {consumer}"
        )

        sequence_no = None
        shard = await self.get_shard()
        shard_id = shard["ShardId"]
        first_run = True

        while True:
            starting_position: StartingPositionTypeDef = {"Type": "LATEST"}
            if first_run and start_sequence_number:
                # Given we are passing a start_sequence_number from the client, we assume the client has already processed this sequence so we want to start after.
                starting_position = {
                    "Type": "AFTER_SEQUENCE_NUMBER",
                    "SequenceNumber": start_sequence_number,
                }
                logging.info(
                    f"Subscribing to shard: {shard_id} after sequence number: {start_sequence_number} (fanout)"
                )
            # Enhanced fanout requires re-sub every 5 minutes,
            # this ensures continuity in the data
            elif sequence_no:
                starting_position = {
                    "Type": "AT_SEQUENCE_NUMBER",
                    "SequenceNumber": sequence_no,
                }
            elif starting_timestamp:
                starting_position = {
                    "Type": "AT_TIMESTAMP",
                    "Timestamp": starting_timestamp,
                }
                logging.info(
                    f"Subscribing to shard: {shard_id} at timestamp: {starting_timestamp} (fanout)"
                )
            else:
                logging.info(
                    f"Subscribing to shard: {shard_id} at LATEST position (fanout)"
                )
            try:
                subscribe_shard_response = (
                    await self._subscribe_to_client_shard_with_backoff(
                        consumer_arn=consumer["arn"],
                        shard_id=shard["ShardId"],
                        starting_position=starting_position,
                    )
                )
                first_run = False
            except ClientError as e:
                code = e.response["Error"]["Code"]
                if (
                    code in {"ResourceNotFoundException", "InvalidArgumentException"}
                    and first_run
                    and start_sequence_number
                ):
                    logging.warning(
                        f"Invalid or expired sequence number, falling back to TRIM_HORIZON: {e}"
                    )
                    client = await self._get_client()
                    subscribe_shard_response = await client.subscribe_to_shard(
                        ConsumerARN=consumer["arn"],
                        ShardId=shard["ShardId"],
                        StartingPosition={"Type": "TRIM_HORIZON"},
                    )
                else:
                    raise

            async for shard_event in subscribe_shard_response["EventStream"]:
                if "SubscribeToShardEvent" in shard_event:
                    for record in shard_event["SubscribeToShardEvent"]["Records"]:
                        # No obvious way to tell compressed data, attempt to decompress
                        # as we intend to default to compression soon anyway
                        try:
                            record_data = zstd.decompress(record["Data"])
                        except zstd.Error:
                            record_data = record["Data"]

                        try:
                            decoded = utils_general.json_loads(record_data)
                            sequence_number = record.get("SequenceNumber")
                            approx_timestamp = record.get("ApproximateArrivalTimestamp")
                            if "data" in decoded:
                                yield self._add_sequence_number_data(
                                    decoded["data"], sequence_number, approx_timestamp
                                )
                            elif "batch" in decoded:
                                for row in decoded["batch"]:
                                    yield self._add_sequence_number_data(
                                        row, sequence_number, approx_timestamp
                                    )
                            else:
                                yield self._add_sequence_number_data(
                                    decoded, sequence_number, approx_timestamp
                                )
                        except Exception as e:
                            logging.exception(f"Failed to handle record: {record} {e}")

                    sequence_no = shard_event["SubscribeToShardEvent"][
                        "ContinuationSequenceNumber"
                    ]

            if (
                shard_event["SubscribeToShardEvent"]["MillisBehindLatest"]
                > WARN_IF_BEHIND_MS
            ):
                msg = f"{self._input_stream_name}/{self._shard_index} behind by {shard_event['SubscribeToShardEvent']['MillisBehindLatest'] / 1000:.2f}s"
                logging.warning(msg)

    def iterate_records(
        self, start_timestamp: datetime | None, start_sequence_number: str | None
    ) -> AsyncIterator[Any]:
        # This check is placed here instead of __init__() to avoid noise
        # on class initialisation (not all Kinesis instances are writes)
        if self._consumer_mode == "fanout" and not IS_ECS and not ECS_INDEX_OVERRIDE:
            logging.warning(
                f"Task NOT running in ECS. Disabling fanout: {self._input_stream_name}/"
                f"{self._shard_index}"
            )
            self._consumer_mode = "unregistered"

        # Returns awaitable, this function itself does not need to be async
        if self._consumer_mode == "fanout":
            return self._iterate_records_fanout(start_timestamp, start_sequence_number)
        elif self._consumer_mode == "unregistered":
            return self._iterate_records_unregistered(
                start_timestamp, start_sequence_number
            )
        else:
            raise NotImplementedError(
                f"Unrecognised consumer mode: {self._consumer_mode}"
            )
