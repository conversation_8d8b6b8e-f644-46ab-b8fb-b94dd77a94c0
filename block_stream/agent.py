from collections.abc import Callable
from datetime import datetime

from block_stream.channel import Channel
from block_stream.config import KINESIS_UPDATE_OUTPUT_STREAMS_SEC
from block_stream.replayer import Replayer
from block_stream.typings import CONSUMER_MODE, OUTPUT_MODE, REPLAY_MODE


class Agent:
    def __init__(
        self,
        name: str,
        endpoint: str | None = None,
        replay_mode: REPLAY_MODE | None = None,
        consumer_mode: CONSUMER_MODE = "unregistered",
        replay_prefix: str | None = None,
    ):
        """
        :param name: Unique name for an agent
        :param endpoint: Optional, use 'localhost' for local testing
        :param replay_mode: Setting this will enable replay/record of incoming messages
        :param consumer_mode: Flag to enable Enhanced Fanout consumer
        :param replay_prefix: File name prefix for replay mode
        """

        self._name = name
        self._endpoint = endpoint
        self._replay_mode: REPLAY_MODE | None = replay_mode
        self._replay_prefix: str | None = replay_prefix
        self._consumer_mode = consumer_mode

    def channel(
        self,
        name: str,
        seconds_per_checkpoint: int | None = None,
        custom_stream: str | None = None,
        shard_index: int = 0,
        auto_flush_s: float | None = None,
        consumer_mode: CONSUMER_MODE = None,
        output_mode: OUTPUT_MODE = None,
        start_timestamp: datetime | None = None,
        start_sequence_number: str | None = None,
        auto_flush_num_records: int | None = 5000,
        list_streams_interval_secs: None | (
            int
        ) = KINESIS_UPDATE_OUTPUT_STREAMS_SEC,  # Set to None to disable, default 1 day
        output_stream_names_filter: Callable[[str], bool] | None = None,
        input_stream_index: int | None = None,
        enable_backup_input_stream: bool = True,
        include_sequence_number: bool = False,
    ) -> Channel:
        """
        Creates and returns a Channel.

        :param name: Consumer name.
        :param seconds_per_checkpoint: Interval in seconds to use in the "unregister consumer" mode to create checkpoints when reading records from the stream.
        :param custom_stream: Specific name of the stream to use. If not provided, the name is constructed based on the `name` parameter.
        :param shard_index: Stream shard index to use.
        :param auto_flush_s: Interval in seconds to automatically flush to the stream.
        :param consumer_mode: Set the consumer mode. Valid modes are "unregistered" or "fanout".
        :param output_mode: Set the mode to use for outputting records. Available options are "single" to output to a single stream, or "multiple" to output to multiple streams.
        :param start_timestamp: Timestamp to use as the starting point for retrieving records from the stream.
        :param start_sequence_number: Sequence number to start consuming records after.
        :param auto_flush_num_records: Number of records to store before automatically flushing to the stream.
        :param list_streams_interval_secs: Interval in seconds to check the available list of streams to output.
        :param output_stream_names_filter: Function to filter output stream names. Returns True if the stream name is valid, False otherwise.
        :param input_stream_index: Index to use when setting the input stream name to consume from. e.g. channel_tickData_{input_stream_index}
        :param enable_backup_input_stream: parameter to enable using backup streams if the stream associated with the :input_stream_index still reaches the fanout consumer limit after retrying. Default True
        :param include_sequence_number: Flag to include sequence number in the output record. Will yield sequence_number as a field in returned dict if set to True.
        :return: Channel object.
        """

        # Leaving self._consumer_mode in place for backwards compatibility
        consumer_mode = consumer_mode or self._consumer_mode

        channel = Channel(
            name=name,
            app_name=self._name,
            seconds_per_checkpoint=seconds_per_checkpoint,
            custom_stream=custom_stream,
            shard_index=shard_index,
            endpoint=self._endpoint,
            auto_flush_s=auto_flush_s,
            consumer_mode=consumer_mode,
            output_mode=output_mode,
            start_timestamp=start_timestamp,
            start_sequence_number=start_sequence_number,
            auto_flush_num_records=auto_flush_num_records,
            list_streams_interval_secs=list_streams_interval_secs,
            output_stream_names_filter=output_stream_names_filter,
            input_stream_index=input_stream_index,
            enable_backup_input_stream=enable_backup_input_stream,
            include_sequence_number=include_sequence_number,
        )
        if self._replay_mode:
            return Replayer(
                channel,
                prefix=self._replay_prefix,
                mode=self._replay_mode,
            )
        else:
            return channel

    def __repr__(self) -> str:
        return f"<Agent {self._name} {self._endpoint=:}>"
