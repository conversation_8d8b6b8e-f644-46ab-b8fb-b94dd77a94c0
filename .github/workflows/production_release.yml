name: Production Release

on:
  pull_request:
    types:
      - closed
    branches:
      - production

jobs:
  release:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    environment: prod
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: refs/heads/production
      - name: Get Previous Staging Tag
        uses: oprypin/find-latest-tag@v1
        with:
          releases-only: true
          prefix: 'v'
          repository: ${{ github.repository }}
        id: staging_latest
      - name: Get Previous Production Tag
        uses: oprypin/find-latest-tag@v1
        with:
          releases-only: true
          prefix: 'prod-'
          repository: ${{ github.repository }}
        id: prod_latest
      - name: Create New Production Tag
        uses: actions/github-script@v5
        with:
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: 'refs/tags/prod-${{ steps.staging_latest.outputs.tag }}',
              sha: context.sha
            })
      - name: Update CHANGELOG
        id: changelog
        uses: requarks/changelog-action@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fromTag: prod-${{ steps.staging_latest.outputs.tag }}
          toTag: ${{ steps.prod_latest.outputs.tag }}
          writeToFile: false
          includeInvalidCommits: true
      - name: Create a GitHub release
        uses: ncipollo/release-action@v1
        with:
          tag: prod-${{ steps.staging_latest.outputs.tag }}
          name: prod-${{ steps.staging_latest.outputs.tag }}
          commit: production
          body: ${{ steps.changelog.outputs.changes }}
