name: Staging Test & Release

on:
  push:
    branches:
      - main
jobs:
  test:
    if: github.repository == 'blockscholes/blockStream'
    uses: ./.github/workflows/test.yml
    secrets: inherit

  release:
    if: github.repository == 'blockscholes/blockStream'
    runs-on: ubuntu-latest
    environment: staging
    strategy:
      matrix:
        python-version: [ "3.11" ]
    needs: test
    steps:
      - uses: actions/checkout@v3
      - name: Bump version and push tag
        id: tag_version
        uses: mathieudutour/github-tag-action@v6.0
        with:
          release_branches: main
          github_token: ${{ secrets.GITHUB_TOKEN }}
      - name: Create a GitHub release
        uses: ncipollo/release-action@v1
        with:
          tag: ${{ steps.tag_version.outputs.new_tag }}
          name: ${{ steps.tag_version.outputs.new_tag }}
          body: ${{ steps.tag_version.outputs.changelog }}
