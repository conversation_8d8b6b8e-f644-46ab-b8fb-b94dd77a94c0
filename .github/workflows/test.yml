name: Test

on:
  pull_request:
    branches:
      - main
  workflow_call:

jobs:
  test:
    if: github.repository == 'blockscholes/blockStream'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies on local build
        run: |
          pip install --upgrade pip
          pip install -r requirements-dev.txt
        env:
          ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }}
      - name: Check for DOS line endings
        run: "! git ls-files | xargs grep -Il $'\r'"
      - name: Check code syntax with Black
        run: black --check .
      - name: Mypy type check
        run: mypy .
      - name: Ruff lint check
        run: ruff .
      - name: Test with pytest
        run: pytest
