import asyncio
import logging
import sys

from block_stream import Agent, config

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logger = logging.getLogger()
agent = Agent("FanoutConsumer")


async def consumer() -> None:
    config.IS_ECS = True

    fanout_consumer = agent.channel("tickData", consumer_mode="fanout")

    async for item in fanout_consumer:
        print(item)


if __name__ == "__main__":
    asyncio.run(consumer())
