import asyncio
import logging
import sys

from block_stream import Agent

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logger = logging.getLogger()
agent = Agent("AsyncConsumer", endpoint="localhost")


async def consumer() -> None:
    first_channel = agent.channel(
        "tickData",
        seconds_per_checkpoint=0,
        include_sequence_number=True,
    )

    async for item in first_channel:
        print(item)


if __name__ == "__main__":
    asyncio.run(consumer())
