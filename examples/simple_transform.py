import asyncio
import logging
import math
import sys

from block_stream import Agent
from block_stream.channel import Channel

logging.basicConfig(stream=sys.stdout, level=logging.INFO)
logger = logging.getLogger()
agent = Agent("SimpleTransform", endpoint="localhost")


async def transformer() -> None:
    first_channel = agent.channel(
        "first",
        seconds_per_checkpoint=10,
    )
    assert isinstance(first_channel, Channel)
    sqrt_channel = agent.channel("sqrt")
    logger.info("Waiting for work")

    async for item in first_channel:
        transformed: dict[str, object] = {"x_sqrt": math.sqrt(item["x"])}
        await sqrt_channel.put(transformed)

        logger.info(f"Transformed and forwarded: {item} -> {transformed}")


if __name__ == "__main__":
    asyncio.run(transformer())
