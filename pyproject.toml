[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
exclude = '(venv/*|env/*)'

[tool.ruff]
line-length = 88
extend-exclude = ['venv', 'env']

[tool.ruff.lint]
select = [
    'F',   # pyflakes
    'E',   # pycodestyle
    'W',   # pycodestyle
    'I',   # isort
    'UP',  # pyupgrade
    'B',   # flake8-bugbear
    'C',   # flake8-comprehensions
    'DTZ', # flake8-datetimez
    'RUF', # ruff
]

ignore = [
    'E501', # line too long, handled by black
    'C901', # complex structure, not needed
]

[tool.ruff.lint.per-file-ignores]
'__init__.py' = [
    'F401', # unused import
    'E402', # module import not at top of file
]

[tool.mypy]
python_version = '3.11'
warn_return_any = true
warn_unused_configs = true
allow_redefinition = false
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = false
strict = true
