# Block Stream library

## Utils

For streaming utils documentation, see [readme.md](block_stream/utils/readme.md)

### How to experiment with the library

* Install locally by running `pip install -e .`
* Run Dynamo + Kinesis locally `docker-compose up`
* See example producers and consumers under `examples/`

### Installing dependencies

For local dev, install dependencies from requirements-dev.txt. A Github access
token is required to install blockscholes dependencies, set via environment
variable:

```
$ export ACCESS_TOKEN=...
$ pip install -r requirements-dev.txt
```


### Profiler

The Profiler Manager is a module designed to provide CPU and memory profiling capabilities for Python applications. It supports both local and ECS environments and includes features such as automatic file rotation, compression, and optional S3 upload for profiling data.

#### Features:
- **Memory Profiling**: Uses `memray` for tracking memory usage.
- **CPU Profiling**: Uses `yappi` for CPU profiling with support for thread-level profiling.
- **Automatic File Rotation**: Profiles are automatically rotated based on a configurable interval.
- **S3 Upload Support**: Profiling data can be uploaded to an S3 bucket in ECS environments.
- **Child Process Profiling**: Supports profiling of child processes with configurable limits.

#### How to Use

1. **Configuration**: Set up the profiler configuration using environment variables. For example:
   ```json
   {
       "enable": true,
       "path": "/path/to/profiles",
       "s3": {
           "s3_bucket": "your-s3-bucket",
           "s3_prefix": "your/s3/prefix"
       },
       "memray": {
           "enable": true,
           "interval_sec": 3600
       },
       "yappi": {
           "enable": true,
           "interval_sec": 3600,
           "clock_type": "cpu",
           "profile_threads": true
       },
       "profile_child_process": true,
       "child_process_count": 2
   }

1. **Enable Profiling**: Use the ProfilerManager as a context manager in your code:


```python
from block_stream.utils.profiler import ProfilerManager

def example_function():
    # Simulate some workload
    for _ in range(1000000):
        pass

if __name__ == "__main__":
    with ProfilerManager():
        example_function()
```

**Output**: Profiling data will be saved to the configured path or uploaded to S3 if running in an ECS environment.

This will generate profiling data for the example_function and save it to the configured location.


##### Required dependencies:
```
memray==1.15.0
yappi==1.6.10
```
