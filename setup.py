# !/usr/bin/env python

from distutils.core import setup

setup(
    name="block_stream",
    version="4.8.1",
    description="Library to build stream processing applications on top of Kinesis",
    author="Block Scholes",
    author_email="<EMAIL>",
    url="https://github.com/blockscholes/blockStream",
    install_requires=[
        "aiobotocore",
        "botocore",
        "mypy-boto3-kinesis",
        "types-aiobotocore[cloudwatch]",
        "types-aiobotocore[kinesis]",
        "types-aiobotocore[ecs]",
        "utils_general",
        "requests",
        "zstd",
        "pandas",
        "nest-asyncio",
        "cachetools",
        "backoff>=2.2.1",
    ],
    packages=["block_stream", "block_stream.consumer", "block_stream.utils"],
    package_data={"block_stream": ["py.typed"]},
    python_requires=">=3.11",
)
